# These are some examples of commonly ignored file patterns.
# You should customize this list as applicable to your project.
# Learn more about .gitignore:
#     https://www.atlassian.com/git/tutorials/saving-changes/gitignore

# AI rules
.augment/*
.cursor/*

# Node artifact files
node_modules/
dist/

# Compiled Java class files
*.class

# Compiled Python bytecode
*.py[cod]

# Log files
*.log

# Package files
*.jar

# Maven
target/
dist/

# JetBrains IDE
.idea/

# Unit test reports
TEST*.xml

# Generated by MacOS
.DS_Store

# Generated by Windows
Thumbs.db

# Applications
*.app
*.exe
*.war

# Large media files
*.mp4
*.tiff
*.avi
*.flv
*.mov
*.wmv

# Terraform ignore file
# Ignore Terraform state files
*.tfstate
*.tfstate.backup
*.tfstate.backup.*
*.tfstate.tmp
*.tfvars

# Ignore .terraform directory
.terraform/

# Ignore override files
override.tf
override.tf.json
*_override.tf
*_override.tf.json

# Ignore plan output files
*.tfplan

# Ignore crash log files
crash.log

# Ignore CLI configuration files
.terraformrc
terraform.rc
.terraform.tfstate.lock.info
bin
obj

# Ignore for Lambda
*/publish/

# Visual Studio Files
.vs/
*.suo
*.user
*.userosscache
*.sln.docstates
*.csproj.user
*.vcxproj.user
*.cachefile

# Visual Studio Code
.vscode/
.history/
infra/modules/lambda-app/sample_lambda.zip
publish/
*.lnk