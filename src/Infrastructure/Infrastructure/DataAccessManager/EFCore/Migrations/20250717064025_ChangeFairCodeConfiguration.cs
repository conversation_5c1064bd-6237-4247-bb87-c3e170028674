﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Infrastructure.DataAccessManager.EFCore.Migrations
{
    /// <inheritdoc />
    public partial class ChangeFairCodeConfiguration : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_FairCoder_IsDeleted",
                table: "Fair");

            migrationBuilder.CreateIndex(
                name: "IX_FairCode_IsDeleted",
                table: "Fair",
                column: "FairCode",
                unique: true,
                filter: "[IsDeleted] = 0");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_FairCode_IsDeleted",
                table: "Fair");

            migrationBuilder.CreateIndex(
                name: "IX_FairCoder_IsDeleted",
                table: "Fair",
                columns: new[] { "FairCode", "IsDeleted" },
                unique: true,
                filter: "[FairCode] IS NOT NULL");
        }
    }
}
