﻿using Application.Common.Repositories;
using Domain.Entities;
using Infrastructure.DataAccessManager.EFCore.Configurations;
using Infrastructure.SecurityManager.AspNetIdentity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.DataAccessManager.EFCore.Contexts;


public class DataContext : IdentityDbContext<ApplicationUser>, IEntityDbSet
{
    public DataContext(DbContextOptions<DataContext> options) : base(options)
    {
    }

    public DbSet<Token> Token { get; set; }
    public DbSet<Company> Company { get; set; }
    public DbSet<FileImage> FileImage { get; set; }
    public DbSet<FileDocument> FileDocument { get; set; }


    public DbSet<NumberSequence> NumberSequence { get; set; }
    public DbSet<CustomerGroup> CustomerGroup { get; set; }
    public DbSet<CustomerCategory> CustomerCategory { get; set; }
    public DbSet<VendorGroup> VendorGroup { get; set; }
    public DbSet<VendorCategory> VendorCategory { get; set; }
    public DbSet<Warehouse> Warehouse { get; set; }
    public DbSet<Customer> Customer { get; set; }
    public DbSet<Vendor> Vendor { get; set; }
    public DbSet<UnitMeasure> UnitMeasure { get; set; }
    public DbSet<ProductGroup> ProductGroup { get; set; }
    public DbSet<Product> Product { get; set; }
    public DbSet<CustomerContact> CustomerContact { get; set; }
    public DbSet<VendorContact> VendorContact { get; set; }
    public DbSet<Tax> Tax { get; set; }
    public DbSet<SalesOrder> SalesOrder { get; set; }
    public DbSet<SalesOrderItem> SalesOrderItem { get; set; }
    public DbSet<PurchaseOrder> PurchaseOrder { get; set; }
    public DbSet<PurchaseOrderItem> PurchaseOrderItem { get; set; }
    public DbSet<InventoryTransaction> InventoryTransaction { get; set; }
    public DbSet<DeliveryOrder> DeliveryOrder { get; set; }
    public DbSet<GoodsReceive> GoodsReceive { get; set; }
    public DbSet<SalesReturn> SalesReturn { get; set; }
    public DbSet<PurchaseReturn> PurchaseReturn { get; set; }
    public DbSet<TransferIn> TransferIn { get; set; }
    public DbSet<TransferOut> TransferOut { get; set; }
    public DbSet<StockCount> StockCount { get; set; }
    public DbSet<NegativeAdjustment> NegativeAdjustment { get; set; }
    public DbSet<PositiveAdjustment> PositiveAdjustment { get; set; }
    public DbSet<Scrapping> Scrapping { get; set; }
    public DbSet<Region> Region { get; set; }
    public DbSet<Location> Location { get; set; }
    public DbSet<Division> Division { get; set; }
    public DbSet<Verifier> Verifier { get; set; }
    public DbSet<HierarchyLevel> HierarchyLevel { get; set; }
    public DbSet<Bank> Bank { get; set; }
    public DbSet<Salesman> Salesman { get; set; }
    public DbSet<ProductCode> ProductCode { get; set; }
    public DbSet<PostingType> PostingType { get; set; }
    public DbSet<CollectorType> CollectorType { get; set; }
    public DbSet<PaymentType> PaymentType { get; set; }
    public DbSet<CollectorActivityRate> CollectorActivityRate { get; set; }
    public DbSet<PriceList> PriceList { get; set; }
    public DbSet<Collector> Collector { get; set; }
    public DbSet<CollectorCode> CollectorCode { get; set; }
    public DbSet<SalesmanBonusType> SalesmanBonusType { get; set; }
    public DbSet<SalesCommissionChargebackRate> SalesCommissionChargebackRate { get; set; }
    public DbSet<Year> Year { get; set; }
    public DbSet<Month> Month { get; set; }
    public DbSet<SalesmanProductionBonusInfo> SalesmanProductionBonusInfo { get; set; }
    public DbSet<SalesmanRallyBonus> SalesmanRallyBonus { get; set; }
    public DbSet<Fair> Fair { get; set; }






    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.ApplyConfiguration(new ApplicationUserConfiguration());
        modelBuilder.ApplyConfiguration(new TokenConfiguration());
        modelBuilder.ApplyConfiguration(new CompanyConfiguration());
        modelBuilder.ApplyConfiguration(new FileImageConfiguration());
        modelBuilder.ApplyConfiguration(new FileDocumentConfiguration());


        modelBuilder.ApplyConfiguration(new NumberSequenceConfiguration());
        modelBuilder.ApplyConfiguration(new CustomerGroupConfiguration());
        modelBuilder.ApplyConfiguration(new CustomerCategoryConfiguration());
        modelBuilder.ApplyConfiguration(new VendorGroupConfiguration());
        modelBuilder.ApplyConfiguration(new VendorCategoryConfiguration());
        modelBuilder.ApplyConfiguration(new WarehouseConfiguration());
        modelBuilder.ApplyConfiguration(new CustomerConfiguration());
        modelBuilder.ApplyConfiguration(new VendorConfiguration());
        modelBuilder.ApplyConfiguration(new UnitMeasureConfiguration());
        modelBuilder.ApplyConfiguration(new ProductGroupConfiguration());
        modelBuilder.ApplyConfiguration(new ProductConfiguration());
        modelBuilder.ApplyConfiguration(new CustomerContactConfiguration());
        modelBuilder.ApplyConfiguration(new VendorContactConfiguration());
        modelBuilder.ApplyConfiguration(new TaxConfiguration());
        modelBuilder.ApplyConfiguration(new SalesOrderConfiguration());
        modelBuilder.ApplyConfiguration(new SalesOrderItemConfiguration());
        modelBuilder.ApplyConfiguration(new PurchaseOrderConfiguration());
        modelBuilder.ApplyConfiguration(new PurchaseOrderItemConfiguration());
        modelBuilder.ApplyConfiguration(new InventoryTransactionConfiguration());
        modelBuilder.ApplyConfiguration(new DeliveryOrderConfiguration());
        modelBuilder.ApplyConfiguration(new GoodsReceiveConfiguration());
        modelBuilder.ApplyConfiguration(new SalesReturnConfiguration());
        modelBuilder.ApplyConfiguration(new PurchaseReturnConfiguration());
        modelBuilder.ApplyConfiguration(new TransferInConfiguration());
        modelBuilder.ApplyConfiguration(new TransferOutConfiguration());
        modelBuilder.ApplyConfiguration(new StockCountConfiguration());
        modelBuilder.ApplyConfiguration(new NegativeAdjustmentConfiguration());
        modelBuilder.ApplyConfiguration(new PositiveAdjustmentConfiguration());
        modelBuilder.ApplyConfiguration(new ScrappingConfiguration());
        modelBuilder.ApplyConfiguration(new RegionConfiguration());
        modelBuilder.ApplyConfiguration(new LocationConfiguration());
        modelBuilder.ApplyConfiguration(new DivisionConfiguration());
        modelBuilder.ApplyConfiguration(new VerifierConfiguration());
        modelBuilder.ApplyConfiguration(new HierarchyLevelConfiguration());
        modelBuilder.ApplyConfiguration(new BankConfiguration());
        modelBuilder.ApplyConfiguration(new SalesmanConfiguration());
        modelBuilder.ApplyConfiguration(new ProductCodeConfiguration());
        modelBuilder.ApplyConfiguration(new PostingTypeConfiguration());
        modelBuilder.ApplyConfiguration(new CollectorTypeConfiguration());
        modelBuilder.ApplyConfiguration(new PaymentTypeConfiguration());
        modelBuilder.ApplyConfiguration(new CollectorActivityRateConfiguration());
        modelBuilder.ApplyConfiguration(new PriceListConfiguration());
        modelBuilder.ApplyConfiguration(new CollectorConfiguration());
        modelBuilder.ApplyConfiguration(new CollectorCodeConfiguration());
        modelBuilder.ApplyConfiguration(new SalesmanBonusTypeConfiguration());
        modelBuilder.ApplyConfiguration(new SalesCommissionChargebackRateConfiguration());
        modelBuilder.ApplyConfiguration(new YearConfiguration());
        modelBuilder.ApplyConfiguration(new MonthConfiguration());
        modelBuilder.ApplyConfiguration(new SalesmanProductionBonusInfoConfiguration());
        modelBuilder.ApplyConfiguration(new SalesmanRallyBonusConfiguration());
        modelBuilder.ApplyConfiguration(new FairConfiguration());


    }
}


