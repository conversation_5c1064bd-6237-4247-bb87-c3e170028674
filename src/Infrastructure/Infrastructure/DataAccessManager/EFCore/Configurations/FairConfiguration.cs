using Domain.Entities;
using Infrastructure.DataAccessManager.EFCore.Common;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using static Domain.Common.Constants;
using Microsoft.EntityFrameworkCore;


namespace Infrastructure.DataAccessManager.EFCore.Configurations
{
    public class FairConfiguration : BaseEntityConfiguration<Fair>
    {
        public override void Configure(EntityTypeBuilder<Fair> builder)
        {
            base.Configure(builder);
            builder.Property(x => x.FairCode).HasMaxLength(NameConsts.MaxLength).IsRequired(false);
            builder.Property(e => e.Description).HasMaxLength(DescriptionConsts.MaxLength).IsRequired(false);
            builder
                .HasIndex(e => e.FairCode)
                .IsUnique()
                .HasDatabaseName("IX_FairCode_IsDeleted")
                .HasFilter("[IsDeleted] = 0");
        }
    }
    
    
}
