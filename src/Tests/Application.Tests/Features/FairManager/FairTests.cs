using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;
using Application.Common.Repositories;
using Application.Features.FairManager.Commands;
using Application.Features.FairManager.Queries;
using AutoMapper;
using Domain.Entities;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Query;
using Moq;
using Xunit;
using Application.Common.CQS.Queries;

namespace Application.Tests.Features.FairManager
{
    public class FairTests
    {
        private readonly Mock<ICommandRepository<Fair>> _mockFairRepository;
        private readonly Mock<ICommandRepository<SalesmanRallyBonus>> _mockRallyBonusRepository;
        private readonly Mock<IUnitOfWork> _mockUnitOfWork;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IQueryContext> _mockQueryContext;

        public FairTests()
        {
            _mockFairRepository = new Mock<ICommandRepository<Fair>>();
            _mockRallyBonusRepository = new Mock<ICommandRepository<SalesmanRallyBonus>>();
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockMapper = new Mock<IMapper>();
            _mockQueryContext = new Mock<IQueryContext>();
        }

        [Fact]
        public async Task GetFairList_ShouldReturnListOfFairs()
        {
            // Arrange
            var fairs = new List<Fair>
            {
                new Fair { Id = "1", FairCode = "F1", Description = "Fair 1", CreatedAtUtc = DateTime.UtcNow },
                new Fair { Id = "2", FairCode = "F2", Description = "Fair 2", CreatedAtUtc = DateTime.UtcNow }
            };

            var dtos = new List<GetFairListDto>
            {
                new GetFairListDto { Id = "1", FairCode = "F1", Description = "Fair 1", CreatedAtUtc = fairs[0].CreatedAtUtc },
                new GetFairListDto { Id = "2", FairCode = "F2", Description = "Fair 2", CreatedAtUtc = fairs[1].CreatedAtUtc }
            };

            var mockDbSet = new Mock<DbSet<Fair>>();
            var queryable = fairs.AsQueryable();

            mockDbSet.As<IQueryable<Fair>>()
                .Setup(m => m.Provider)
                .Returns(new TestAsyncQueryProvider<Fair>(queryable.Provider));
            mockDbSet.As<IQueryable<Fair>>()
                .Setup(m => m.Expression)
                .Returns(queryable.Expression);
            mockDbSet.As<IQueryable<Fair>>()
                .Setup(m => m.ElementType)
                .Returns(queryable.ElementType);
            mockDbSet.As<IQueryable<Fair>>()
                .Setup(m => m.GetEnumerator())
                .Returns(queryable.GetEnumerator());
            mockDbSet.As<IAsyncEnumerable<Fair>>()
                .Setup(m => m.GetAsyncEnumerator(It.IsAny<CancellationToken>()))
                .Returns(new TestAsyncEnumerator<Fair>(fairs.GetEnumerator()));

            _mockQueryContext.Setup(x => x.Fair)
                .Returns(mockDbSet.Object);

            _mockMapper.Setup(x => x.Map<List<GetFairListDto>>(fairs))
                .Returns(dtos);

            var handler = new GetFairListHandler(_mockMapper.Object, _mockQueryContext.Object);
            var request = new GetFairListRequest();

            // Act
            var result = await handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.NotNull(result.Data);
            Assert.Equal(2, result.Data.Count);
            Assert.Equal("F1", result.Data[0].FairCode);
            Assert.Equal("F2", result.Data[1].FairCode);
        }

        [Fact]
        public async Task CreateFair_ShouldCreateNewFair()
        {
            // Arrange
            var request = new CreateFairRequest
            {
                FairCode = "F1",
                Description = "Test Fair",
                CreatedById = "user1"
            };

            var mockDbSet = new Mock<DbSet<Fair>>();
            var fairs = new List<Fair>().AsQueryable();

            mockDbSet.As<IQueryable<Fair>>()
                .Setup(m => m.Provider)
                .Returns(new TestAsyncQueryProvider<Fair>(fairs.Provider));
            mockDbSet.As<IQueryable<Fair>>()
                .Setup(m => m.Expression)
                .Returns(fairs.Expression);
            mockDbSet.As<IQueryable<Fair>>()
                .Setup(m => m.ElementType)
                .Returns(fairs.ElementType);
            mockDbSet.As<IQueryable<Fair>>()
                .Setup(m => m.GetEnumerator())
                .Returns(fairs.GetEnumerator());
            mockDbSet.As<IAsyncEnumerable<Fair>>()
                .Setup(m => m.GetAsyncEnumerator(It.IsAny<CancellationToken>()))
                .Returns(new TestAsyncEnumerator<Fair>(fairs.GetEnumerator()));

            _mockFairRepository.Setup(x => x.GetQuery())
                .Returns(mockDbSet.Object);

            var handler = new CreateFairHandler(_mockFairRepository.Object, _mockUnitOfWork.Object);

            // Act
            var result = await handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.NotNull(result.Data);
            Assert.Equal("F1", result.Data.FairCode);
            Assert.Equal("Test Fair", result.Data.Description);
            _mockFairRepository.Verify(x => x.CreateAsync(It.IsAny<Fair>(), It.IsAny<CancellationToken>()), Times.Once);
            _mockUnitOfWork.Verify(x => x.SaveAsync(It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task CreateFair_WithInvalidData_ShouldFailValidation()
        {
            // Arrange
            var validator = new CreateFairValidator();
            var request = new CreateFairRequest(); // Empty request

            // Act
            var result = await validator.ValidateAsync(request);

            // Assert
            Assert.False(result.IsValid);
            Assert.Contains(result.Errors, e => e.PropertyName == "FairCode");
            Assert.Contains(result.Errors, e => e.PropertyName == "Description");
        }

        [Fact]
        public async Task UpdateFair_ShouldUpdateExistingFair()
        {
            // Arrange
            var request = new UpdateFairRequest
            {
                Id = "1",
                FairCode = "F1",
                Description = "Updated Fair",
                UpdatedById = "user1"
            };

            var existingFair = new Fair
            {
                Id = "1",
                FairCode = "F1-old",
                Description = "Original Fair"
            };

            _mockFairRepository.Setup(x => x.GetAsync(request.Id, It.IsAny<CancellationToken>()))
                .ReturnsAsync(existingFair);

            var handler = new UpdateFairHandler(_mockFairRepository.Object, _mockUnitOfWork.Object);

            // Act
            var result = await handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.NotNull(result.Data);
            Assert.Equal("F1", result.Data.FairCode);
            Assert.Equal("Updated Fair", result.Data.Description);
            _mockFairRepository.Verify(x => x.Update(It.IsAny<Fair>()), Times.Once);
            _mockUnitOfWork.Verify(x => x.SaveAsync(It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task UpdateFair_ShouldThrowException_WhenFairNotFound()
        {
            // Arrange
            var request = new UpdateFairRequest { Id = "1" };

            _mockFairRepository.Setup(x => x.GetAsync(request.Id, It.IsAny<CancellationToken>()))
                .ReturnsAsync((Fair)null);

            var handler = new UpdateFairHandler(_mockFairRepository.Object, _mockUnitOfWork.Object);

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(() =>
                handler.Handle(request, CancellationToken.None));
        }

        [Fact]
        public async Task DeleteFair_ShouldDeleteExistingFair()
        {
            // Arrange
            var existingFair = new Fair
            {
                Id = "1",
                FairCode = "F1",
                Description = "Test Fair"
            };

            var request = new DeleteFairRequest
            {
                Id = "1",
                DeletedById = "user1"
            };

            _mockFairRepository.Setup(x => x.GetAsync(request.Id, It.IsAny<CancellationToken>()))
                .ReturnsAsync(existingFair);

            var handler = new DeleteFairHandler(_mockFairRepository.Object, _mockUnitOfWork.Object);

            // Act
            var result = await handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.NotNull(result.Data);
            Assert.Equal("1", result.Data.Id);
            Assert.Equal("user1", result.Data.UpdatedById);
            _mockFairRepository.Verify(x => x.Delete(It.IsAny<Fair>()), Times.Once);
            _mockUnitOfWork.Verify(x => x.SaveAsync(It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task DeleteFair_ShouldThrowException_WhenFairNotFound()
        {
            // Arrange
            var request = new DeleteFairRequest { Id = "1" };

            _mockFairRepository.Setup(x => x.GetAsync(request.Id, It.IsAny<CancellationToken>()))
                .ReturnsAsync((Fair)null);

            var handler = new DeleteFairHandler(_mockFairRepository.Object, _mockUnitOfWork.Object);

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(() =>
                handler.Handle(request, CancellationToken.None));
        }

        [Fact]
        public async Task DeleteFair_WithInvalidData_ShouldFailValidation()
        {
            // Arrange
            var validator = new DeleteFairValidator();
            var request = new DeleteFairRequest(); // Empty request

            // Act
            var result = await validator.ValidateAsync(request);

            // Assert
            Assert.False(result.IsValid);
            Assert.Contains(result.Errors, e => e.PropertyName == "Id");
        }
    }

    // Helper classes for async query mocking
    internal class TestAsyncQueryProvider<TEntity> : IAsyncQueryProvider
    {
        private readonly IQueryProvider _inner;

        internal TestAsyncQueryProvider(IQueryProvider inner)
        {
            _inner = inner;
        }

        public IQueryable CreateQuery(Expression expression)
        {
            return new TestAsyncEnumerable<TEntity>(expression);
        }

        public IQueryable<TElement> CreateQuery<TElement>(Expression expression)
        {
            return new TestAsyncEnumerable<TElement>(expression);
        }

        public object Execute(Expression expression)
        {
            return _inner.Execute(expression);
        }

        public TResult Execute<TResult>(Expression expression)
        {
            return _inner.Execute<TResult>(expression);
        }

        public IAsyncEnumerable<TResult> ExecuteAsync<TResult>(Expression expression)
        {
            return new TestAsyncEnumerable<TResult>(expression);
        }

        public TResult ExecuteAsync<TResult>(Expression expression, CancellationToken cancellationToken)
        {
            var expectedResultType = typeof(TResult).GetGenericArguments()[0];
            var executeMethod = typeof(IQueryProvider)
                .GetMethods()
                .First(m => m.Name == nameof(IQueryProvider.Execute) && m.IsGenericMethod)
                .MakeGenericMethod(expectedResultType);

            var executionResult = executeMethod.Invoke(_inner, new[] { expression });

            var taskFromResultMethod = typeof(Task)
                .GetMethods()
                .First(m => m.Name == nameof(Task.FromResult) && m.IsGenericMethod)
                .MakeGenericMethod(expectedResultType);

            return (TResult)taskFromResultMethod.Invoke(null, new[] { executionResult });
        }
    }

    internal class TestAsyncEnumerable<T> : EnumerableQuery<T>, IAsyncEnumerable<T>, IQueryable<T>
    {
        public TestAsyncEnumerable(IEnumerable<T> enumerable)
            : base(enumerable)
        { }

        public TestAsyncEnumerable(Expression expression)
            : base(expression)
        { }

        public IAsyncEnumerator<T> GetAsyncEnumerator(CancellationToken cancellationToken = default)
        {
            return new TestAsyncEnumerator<T>(this.AsEnumerable().GetEnumerator());
        }

        IQueryProvider IQueryable.Provider
        {
            get { return new TestAsyncQueryProvider<T>(this); }
        }
    }

    internal class TestAsyncEnumerator<T> : IAsyncEnumerator<T>
    {
        private readonly IEnumerator<T> _inner;

        public TestAsyncEnumerator(IEnumerator<T> inner)
        {
            _inner = inner;
        }

        public T Current => _inner.Current;

        public ValueTask<bool> MoveNextAsync()
        {
            return new ValueTask<bool>(_inner.MoveNext());
        }

        public ValueTask DisposeAsync()
        {
            _inner.Dispose();
            return new ValueTask();
        }
    }
} 