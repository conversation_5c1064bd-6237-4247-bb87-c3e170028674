﻿using Domain.Entities;
using Microsoft.EntityFrameworkCore;

namespace Application.Common.Repositories;


public interface IEntityDbSet
{
    public DbSet<Token> Token { get; set; }
    public DbSet<Company> Company { get; set; }
    public DbSet<FileImage> FileImage { get; set; }
    public DbSet<FileDocument> FileDocument { get; set; }
    public DbSet<NumberSequence> NumberSequence { get; set; }
    public DbSet<CustomerGroup> CustomerGroup { get; set; }
    public DbSet<CustomerCategory> CustomerCategory { get; set; }
    public DbSet<VendorGroup> VendorGroup { get; set; }
    public DbSet<VendorCategory> VendorCategory { get; set; }
    public DbSet<Warehouse> Warehouse { get; set; }
    public DbSet<Customer> Customer { get; set; }
    public DbSet<Vendor> Vendor { get; set; }
    public DbSet<UnitMeasure> UnitMeasure { get; set; }
    public DbSet<ProductGroup> ProductGroup { get; set; }
    public DbSet<Product> Product { get; set; }
    public DbSet<CustomerContact> CustomerContact { get; set; }
    public DbSet<VendorContact> VendorContact { get; set; }
    public DbSet<Tax> Tax { get; set; }
    public DbSet<SalesOrder> SalesOrder { get; set; }
    public DbSet<SalesOrderItem> SalesOrderItem { get; set; }
    public DbSet<PurchaseOrder> PurchaseOrder { get; set; }
    public DbSet<PurchaseOrderItem> PurchaseOrderItem { get; set; }
    public DbSet<InventoryTransaction> InventoryTransaction { get; set; }
    public DbSet<DeliveryOrder> DeliveryOrder { get; set; }
    public DbSet<GoodsReceive> GoodsReceive { get; set; }
    public DbSet<SalesReturn> SalesReturn { get; set; }
    public DbSet<PurchaseReturn> PurchaseReturn { get; set; }
    public DbSet<TransferIn> TransferIn { get; set; }
    public DbSet<TransferOut> TransferOut { get; set; }
    public DbSet<StockCount> StockCount { get; set; }
    public DbSet<NegativeAdjustment> NegativeAdjustment { get; set; }
    public DbSet<PositiveAdjustment> PositiveAdjustment { get; set; }
    public DbSet<Scrapping> Scrapping { get; set; }
    public DbSet<Region> Region { get; set; }
    public DbSet<Location> Location { get; set; }
    public DbSet<Division> Division { get; set; }
    public DbSet<Verifier> Verifier { get; set; }
    public DbSet<HierarchyLevel> HierarchyLevel { get; set; }
    public DbSet<Bank> Bank { get; set; }
    public DbSet<Salesman> Salesman { get; set; }
    public DbSet<ProductCode> ProductCode { get; set; }
    public DbSet<PostingType> PostingType { get; set; }
    public DbSet<CollectorType> CollectorType { get; set; }
    public DbSet<PaymentType> PaymentType { get; set; }
    public DbSet<CollectorActivityRate> CollectorActivityRate { get; set; }
    public DbSet<PriceList> PriceList { get; set; }
    public DbSet<Collector> Collector { get; set; }
    public DbSet<CollectorCode> CollectorCode { get; set; }
    public DbSet<SalesmanBonusType> SalesmanBonusType { get; set; }
    public DbSet<SalesCommissionChargebackRate> SalesCommissionChargebackRate { get; set; }
    public DbSet<Year> Year { get; set; }
    public DbSet<Month> Month { get; set; }
    public DbSet<SalesmanProductionBonusInfo> SalesmanProductionBonusInfo { get; set; }
    public DbSet<SalesmanRallyBonus> SalesmanRallyBonus { get; set; }
    public DbSet<Fair> Fair { get; set; }


}

