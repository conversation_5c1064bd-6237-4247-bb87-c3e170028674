﻿namespace Application.Common.Repositories;

public interface IUnitOfWork : IDisposable
{
    Task SaveAsync(CancellationToken cancellationToken = default);
    void Save();
    
    // Transaction support
    Task BeginTransactionAsync(CancellationToken cancellationToken = default);
    Task CommitTransactionAsync(CancellationToken cancellationToken = default);
    Task RollbackTransactionAsync(CancellationToken cancellationToken = default);
    bool HasActiveTransaction { get; }

    // Savepoint support
    Task<string> CreateSavepointAsync(string name, CancellationToken cancellationToken = default);
    Task RollbackToSavepointAsync(string name, CancellationToken cancellationToken = default);
    Task ReleaseSavepointAsync(string name, CancellationToken cancellationToken = default);
    IReadOnlyList<string> GetSavepoints();
}
