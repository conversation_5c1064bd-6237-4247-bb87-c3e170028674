using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.SalesmanProductionBonusInfoManager.Commands;

public class CreateOrUpdateSalesmanProductionBonusInfoResult
{
    public List<SalesmanProductionBonusInfo> Data { get; set; } = new();
    public int CreatedCount { get; set; }
    public int UpdatedCount { get; set; }
}

public class CreateOrUpdateSalesmanProductionBonusInfoItem
{
    public string? Id { get; set; }
    public decimal? FourWeeksMonthPoints { get; set; }
    public decimal? FiveWeeksMonthPoints { get; set; }
    public decimal? BonusPercentage { get; set; }
    public string? HierarchyLevelId { get; set; }

}

public class CreateOrUpdateSalesmanProductionBonusInfoRequest : IRequest<CreateOrUpdateSalesmanProductionBonusInfoResult>
{
    public List<CreateOrUpdateSalesmanProductionBonusInfoItem> SalesmanProductionBonusInfos { get; set; } = new();
    public string? UserId { get; set; }
}

public class CreateOrUpdateSalesmanProductionBonusInfoValidator : AbstractValidator<CreateOrUpdateSalesmanProductionBonusInfoRequest>
{
    public CreateOrUpdateSalesmanProductionBonusInfoValidator()
    {
        RuleFor(x => x.SalesmanProductionBonusInfos).NotEmpty().WithMessage("At least one Salesman Production Bonus Info is required.");
        RuleFor(x => x.UserId).NotEmpty().WithMessage("User ID is required.");
        
        RuleForEach(x => x.SalesmanProductionBonusInfos).ChildRules(item =>
        {
            item.RuleFor(x => x.HierarchyLevelId).NotEmpty().WithMessage("Hierarchy Level ID is required.");
            item.RuleFor(x => x.FourWeeksMonthPoints).NotEmpty().WithMessage("Four Weeks Month Points is required.");
            item.RuleFor(x => x.FiveWeeksMonthPoints).NotEmpty().WithMessage("Five Weeks Month Points is required.");
            item.RuleFor(x => x.BonusPercentage).NotEmpty().WithMessage("Bonus Percentage is required.");
        });
    }
}

public class CreateOrUpdateSalesmanProductionBonusInfoHandler : IRequestHandler<CreateOrUpdateSalesmanProductionBonusInfoRequest, CreateOrUpdateSalesmanProductionBonusInfoResult>
{
    private readonly ICommandRepository<SalesmanProductionBonusInfo> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public CreateOrUpdateSalesmanProductionBonusInfoHandler(
        ICommandRepository<SalesmanProductionBonusInfo> repository,
        IUnitOfWork unitOfWork)
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<CreateOrUpdateSalesmanProductionBonusInfoResult> Handle(CreateOrUpdateSalesmanProductionBonusInfoRequest request, CancellationToken cancellationToken = default)
    {
        var result = new CreateOrUpdateSalesmanProductionBonusInfoResult();
        var processedEntities = new List<SalesmanProductionBonusInfo>();

        // Get all existing salesman production bonus info for duplicate checking
        var existingCodes = await _repository.GetQuery()
            .Where(x => !x.IsDeleted)
            .ToListAsync(cancellationToken);

        foreach (var item in request.SalesmanProductionBonusInfos)
        {
            var isNewRecord = string.IsNullOrEmpty(item.Id) || item.Id.StartsWith("gid");
            
            if (isNewRecord)
            {
                await ProcessCreateOperation(item, request.UserId, existingCodes, processedEntities, result, cancellationToken);
            }
            else
            {
                await ProcessUpdateOperation(item, request.UserId, existingCodes, processedEntities, result, cancellationToken);
            }
        }

        await _unitOfWork.SaveAsync(cancellationToken);
        result.Data = processedEntities;

        return result;
    }

    private async Task ProcessCreateOperation(
        CreateOrUpdateSalesmanProductionBonusInfoItem item,
        string? userId,
        List<SalesmanProductionBonusInfo> existingCodes,
        List<SalesmanProductionBonusInfo> processedEntities,
        CreateOrUpdateSalesmanProductionBonusInfoResult result,
        CancellationToken cancellationToken)
    {
        var entity = new SalesmanProductionBonusInfo
        {
            CreatedById = userId,
            FourWeeksMonthPoints = item.FourWeeksMonthPoints,
            FiveWeeksMonthPoints = item.FiveWeeksMonthPoints,
            BonusPercentage = item.BonusPercentage,
            HierarchyLevelId = item.HierarchyLevelId,
        };

        await _repository.CreateAsync(entity, cancellationToken);
        processedEntities.Add(entity);
        result.CreatedCount++;
    }

    private async Task ProcessUpdateOperation(
        CreateOrUpdateSalesmanProductionBonusInfoItem item,
        string? userId,
        List<SalesmanProductionBonusInfo> existingCodes,
        List<SalesmanProductionBonusInfo> processedEntities,
        CreateOrUpdateSalesmanProductionBonusInfoResult result,
        CancellationToken cancellationToken)
    {
        var entity = await _repository.GetAsync(item.Id ?? string.Empty, cancellationToken);
        
        if (entity == null)
        {
            throw new Exception($"Salesman Production Bonus Info with ID '{item.Id}' not found.");
        }


        entity.UpdatedById = userId;
        entity.FourWeeksMonthPoints = item.FourWeeksMonthPoints;
        entity.FiveWeeksMonthPoints = item.FiveWeeksMonthPoints;
        entity.BonusPercentage = item.BonusPercentage;
        entity.HierarchyLevelId = item.HierarchyLevelId;

        _repository.Update(entity);
        processedEntities.Add(entity);
        result.UpdatedCount++;
    }
} 