﻿using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;

namespace Application.Features.SalesmanProductionBonusInfoManager.Commands;

public class DeleteSalesmanProductionBonusInfoResult
{
    public SalesmanProductionBonusInfo? Data { get; set; }
}

public class DeleteSalesmanProductionBonusInfoRequest : IRequest<DeleteSalesmanProductionBonusInfoResult>
{
    public string? Id { get; init; }
    public string? DeletedById { get; init; }
}

public class DeleteSalesmanProductionBonusInfoValidator : AbstractValidator<DeleteSalesmanProductionBonusInfoRequest>
{
    public DeleteSalesmanProductionBonusInfoValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
    }
}

public class DeleteSalesmanProductionBonusInfoHandler : IRequestHandler<DeleteSalesmanProductionBonusInfoRequest, DeleteSalesmanProductionBonusInfoResult>
{
    private readonly ICommandRepository<SalesmanProductionBonusInfo> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public DeleteSalesmanProductionBonusInfoHandler(
        ICommandRepository<SalesmanProductionBonusInfo> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<DeleteSalesmanProductionBonusInfoResult> Handle(DeleteSalesmanProductionBonusInfoRequest request, CancellationToken cancellationToken)
    {

        var entity = await _repository.GetAsync(request.Id ?? string.Empty, cancellationToken);

        if (entity == null)
        {
            throw new Exception($"Entity not found: {request.Id}");
        }

        entity.UpdatedById = request.DeletedById;

        _repository.Delete(entity);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new DeleteSalesmanProductionBonusInfoResult
        {
            Data = entity
        };
    }
}

