﻿using Application.Common.CQS.Queries;
using Application.Common.Extensions;
using AutoMapper;
using Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.SalesmanProductionBonusInfoManager.Queries;

public record GetSalesmanProductionBonusInfoListDto
{
    public string? Id { get; init; }
    public decimal? FourWeeksMonthPoints { get; init; }
    public decimal? FiveWeeksMonthPoints { get; init; }
    public decimal? BonusPercentage { get; init; }
    public string? HierarchyLevelId { get; init; }
    public string? LevelCode { get; init; } // From navigation property for display purposes
    public string? Position { get; init; } // From navigation property for display purposes
    public DateTime? CreatedAtUtc { get; init; }
}

public class GetSalesmanProductionBonusInfoListProfile : Profile
{
    public GetSalesmanProductionBonusInfoListProfile()
    {
        CreateMap<SalesmanProductionBonusInfo, GetSalesmanProductionBonusInfoListDto>()
            .ForMember(dest => dest.LevelCode, opt => opt.MapFrom(src => src.HierarchyLevel != null ? src.HierarchyLevel.LevelCode : null))
            .ForMember(dest => dest.Position, opt => opt.MapFrom(src => src.HierarchyLevel != null ? src.HierarchyLevel.Position : null));
    }
}

public class GetSalesmanProductionBonusInfoListResult
{
    public List<GetSalesmanProductionBonusInfoListDto>? Data { get; init; }
}

public class GetSalesmanProductionBonusInfoListRequest : IRequest<GetSalesmanProductionBonusInfoListResult>
{
    public bool IsDeleted { get; init; } = false;
}


public class GetSalesmanProductionBonusInfoListHandler : IRequestHandler<GetSalesmanProductionBonusInfoListRequest, GetSalesmanProductionBonusInfoListResult>
{
    private readonly IMapper _mapper;
    private readonly IQueryContext _context;

    public GetSalesmanProductionBonusInfoListHandler(IMapper mapper, IQueryContext context)
    {
        _mapper = mapper;
        _context = context;
    }

    public async Task<GetSalesmanProductionBonusInfoListResult> Handle(GetSalesmanProductionBonusInfoListRequest request, CancellationToken cancellationToken)
    {
        var query = _context
            .SalesmanProductionBonusInfo
            .Include(x => x.HierarchyLevel)
            .AsNoTracking()
            .IsDeletedEqualTo(request.IsDeleted)
            .AsQueryable();

        var entities = await query.ToListAsync(cancellationToken);

        var dtos = _mapper.Map<List<GetSalesmanProductionBonusInfoListDto>>(entities);

        return new GetSalesmanProductionBonusInfoListResult
        {
            Data = dtos
        };
    }


}



