using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;

namespace Application.Features.SalesmanBonusTypeManager.Commands;

public class UpdateSalesmanBonusTypeResult
{
    public SalesmanBonusType? Data { get; set; }
}

public class UpdateSalesmanBonusTypeRequest : IRequest<UpdateSalesmanBonusTypeResult>
{
    public string? Id { get; init; }
    public string? BonusCode { get; init; }
    public string? BonusDescription { get; init; }
    public decimal BonusRate { get; init; }
    public int? DaysEligibleFromDeliveryDate { get; init; }
    public bool IsActive { get; set; } = true;
    public string? UpdatedById { get; init; }
}

public class UpdateSalesmanBonusTypeValidator : AbstractValidator<UpdateSalesmanBonusTypeRequest>
{
    public UpdateSalesmanBonusTypeValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
        RuleFor(x => x.BonusCode).NotEmpty();
        RuleFor(x => x.BonusDescription).NotEmpty();
        RuleFor(x => x.BonusRate).NotNull().GreaterThanOrEqualTo(0);
        RuleFor(x => x.DaysEligibleFromDeliveryDate).NotNull().GreaterThanOrEqualTo(0);
        RuleFor(x => x.IsActive).NotNull();
    }
}

public class UpdateSalesmanBonusTypeHandler : IRequestHandler<UpdateSalesmanBonusTypeRequest, UpdateSalesmanBonusTypeResult>
{
    private readonly ICommandRepository<SalesmanBonusType> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public UpdateSalesmanBonusTypeHandler(
        ICommandRepository<SalesmanBonusType> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<UpdateSalesmanBonusTypeResult> Handle(UpdateSalesmanBonusTypeRequest request, CancellationToken cancellationToken)
    {
        var entity = await _repository.GetAsync(request.Id ?? string.Empty, cancellationToken);

        if (entity == null)
        {
            throw new Exception($"Entity not found: {request.Id}");
        }

        entity.UpdatedById = request.UpdatedById;

        entity.BonusCode = request.BonusCode;
        entity.BonusDescription = request.BonusDescription;
        entity.BonusRate = request.BonusRate;
        entity.DaysEligibleFromDeliveryDate = request.DaysEligibleFromDeliveryDate;
        entity.IsActive = request.IsActive;
        
        _repository.Update(entity);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new UpdateSalesmanBonusTypeResult
        {
            Data = entity
        };
    }
} 