using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;

namespace Application.Features.SalesmanBonusTypeManager.Commands;

public class DeleteSalesmanBonusTypeResult
{
    public SalesmanBonusType? Data { get; set; }
}

public class DeleteSalesmanBonusTypeRequest : IRequest<DeleteSalesmanBonusTypeResult>
{
    public string? Id { get; init; }
    public string? DeletedById { get; init; }
}

public class DeleteSalesmanBonusTypeValidator : AbstractValidator<DeleteSalesmanBonusTypeRequest>
{
    public DeleteSalesmanBonusTypeValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
    }
}

public class DeleteSalesmanBonusTypeHandler : IRequestHandler<DeleteSalesmanBonusTypeRequest, DeleteSalesmanBonusTypeResult>
{
    private readonly ICommandRepository<SalesmanBonusType> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public DeleteSalesmanBonusTypeHandler(
        ICommandRepository<SalesmanBonusType> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<DeleteSalesmanBonusTypeResult> Handle(DeleteSalesmanBonusTypeRequest request, CancellationToken cancellationToken)
    {
        var entity = await _repository.GetAsync(request.Id ?? string.Empty, cancellationToken);

        if (entity == null)
        {
            throw new Exception($"Entity not found: {request.Id}");
        }

        entity.UpdatedById = request.DeletedById;

        _repository.Delete(entity);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new DeleteSalesmanBonusTypeResult
        {
            Data = entity
        };
    }
} 