using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.SalesmanBonusTypeManager.Commands;

public class CreateSalesmanBonusTypeResult
{
    public SalesmanBonusType? Data { get; set; }
}

public class CreateSalesmanBonusTypeRequest : IRequest<CreateSalesmanBonusTypeResult>
{
    public string? BonusCode { get; init; }
    public string? BonusDescription { get; init; }
    public decimal? BonusRate { get; init; }
    public int? DaysEligibleFromDeliveryDate { get; init; }
    public bool IsActive { get; set; } = true;

    public string? CreatedById { get; init; }
}

public class CreateSalesmanBonusTypeValidator : AbstractValidator<CreateSalesmanBonusTypeRequest>
{
    public CreateSalesmanBonusTypeValidator()
    {
        RuleFor(x => x.BonusCode).NotEmpty();
        RuleFor(x => x.BonusDescription).NotEmpty();
        RuleFor(x => x.BonusRate).NotNull().GreaterThanOrEqualTo(0);
        RuleFor(x => x.DaysEligibleFromDeliveryDate).NotNull().GreaterThanOrEqualTo(0);
        RuleFor(x => x.IsActive).NotNull();
    }
}

public class CreateSalesmanBonusTypeHandler : IRequestHandler<CreateSalesmanBonusTypeRequest, CreateSalesmanBonusTypeResult>
{
    private readonly ICommandRepository<SalesmanBonusType> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public CreateSalesmanBonusTypeHandler(
        ICommandRepository<SalesmanBonusType> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<CreateSalesmanBonusTypeResult> Handle(CreateSalesmanBonusTypeRequest request, CancellationToken cancellationToken = default)
    {
        // Check for duplicate BonusTypeCode
        var existingBonusType = await _repository.GetQuery()
            .Where(x => x.BonusCode == request.BonusCode && !x.IsDeleted)
            .FirstOrDefaultAsync(cancellationToken);

        if (existingBonusType != null)
        {
            throw new Exception($"Bonus Code :'{request.BonusCode}' already exists.");
        }

        var entity = new SalesmanBonusType();
        entity.CreatedById = request.CreatedById;

        entity.BonusCode = request.BonusCode;
        entity.BonusDescription = request.BonusDescription;
        entity.BonusRate = request.BonusRate;
        entity.DaysEligibleFromDeliveryDate = request.DaysEligibleFromDeliveryDate;
        entity.IsActive = request.IsActive;
        await _repository.CreateAsync(entity, cancellationToken);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new CreateSalesmanBonusTypeResult
        {
            Data = entity
        };
    }
} 