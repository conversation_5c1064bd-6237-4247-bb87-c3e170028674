using Application.Common.CQS.Queries;
using Application.Common.Extensions;
using AutoMapper;
using Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.SalesmanBonusTypeManager.Queries;

public record GetSalesmanBonusTypeListDto
{
    public string? Id { get; init; }
    public string? BonusCode { get; init; }
    public string? BonusDescription { get; init; }
    public decimal BonusRate { get; init; }
    public int? DaysEligibleFromDeliveryDate { get; init; }
    public bool IsActive { get; init; }
    public DateTime? CreatedAtUtc { get; init; }
}

public class GetSalesmanBonusTypeListProfile : Profile
{
    public GetSalesmanBonusTypeListProfile()
    {
        CreateMap<SalesmanBonusType, GetSalesmanBonusTypeListDto>();
    }
}

public class GetSalesmanBonusTypeListResult
{
    public List<GetSalesmanBonusTypeListDto>? Data { get; init; }
}

public class GetSalesmanBonusTypeListRequest : IRequest<GetSalesmanBonusTypeListResult>
{
    public bool IsDeleted { get; init; } = false;
}

public class GetSalesmanBonusTypeListHandler : IRequestHandler<GetSalesmanBonusTypeListRequest, GetSalesmanBonusTypeListResult>
{
    private readonly IMapper _mapper;
    private readonly IQueryContext _context;

    public GetSalesmanBonusTypeListHandler(IMapper mapper, IQueryContext context)
    {
        _mapper = mapper;
        _context = context;
    }

    public async Task<GetSalesmanBonusTypeListResult> Handle(GetSalesmanBonusTypeListRequest request, CancellationToken cancellationToken)
    {
        var query = _context
            .SalesmanBonusType
            .AsNoTracking()
            .IsDeletedEqualTo(request.IsDeleted)
            .AsQueryable();

        var entities = await query.ToListAsync(cancellationToken);

        var dtos = _mapper.Map<List<GetSalesmanBonusTypeListDto>>(entities);

        return new GetSalesmanBonusTypeListResult
        {
            Data = dtos
        };
    }
} 