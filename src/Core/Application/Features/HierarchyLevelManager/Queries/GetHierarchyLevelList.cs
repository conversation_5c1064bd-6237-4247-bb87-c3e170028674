using Application.Common.CQS.Queries;
using Application.Common.Extensions;
using AutoMapper;
using Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.HierarchyLevelManager.Queries;

public record GetHierarchyLevelListDto
{
    public string? Id { get; init; }
    public string? LevelCode { get; init; }
    public string? Position { get; init; }
    public decimal CommissionRate { get; init; }
    public decimal OverridingRate { get; init; }
    public decimal ReserveRate { get; init; }
    public decimal ReserveCap { get; init; }
    public DateTime? CreatedAtUtc { get; init; }
}

public class GetHierarchyLevelListProfile : Profile
{
    public GetHierarchyLevelListProfile()
    {
        CreateMap<HierarchyLevel, GetHierarchyLevelListDto>();
    }
}

public class GetHierarchyLevelListResult
{
    public List<GetHierarchyLevelListDto>? Data { get; init; }
}

public class GetHierarchyLevelListRequest : IRequest<GetHierarchyLevelListResult>
{
    public bool IsDeleted { get; init; } = false;
}

public class GetHierarchyLevelListHandler : IRequestHandler<GetHierarchyLevelListRequest, GetHierarchyLevelListResult>
{
    private readonly IMapper _mapper;
    private readonly IQueryContext _context;

    public GetHierarchyLevelListHandler(IMapper mapper, IQueryContext context)
    {
        _mapper = mapper;
        _context = context;
    }

    public async Task<GetHierarchyLevelListResult> Handle(GetHierarchyLevelListRequest request, CancellationToken cancellationToken)
    {
        var query = _context
            .HierarchyLevel
            .AsNoTracking()
            .IsDeletedEqualTo(request.IsDeleted)
            .AsQueryable();

        var entities = await query.ToListAsync(cancellationToken);

        var dtos = _mapper.Map<List<GetHierarchyLevelListDto>>(entities);

        return new GetHierarchyLevelListResult
        {
            Data = dtos
        };
    }
} 