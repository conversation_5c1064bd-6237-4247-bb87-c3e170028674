using Application.Common.CQS.Queries;
using Application.Common.Extensions;
using AutoMapper;
using Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.HierarchyLevelManager.Queries;

public class GetHierarchyLevelWithoutProductionBonusInfoListResult
{
    public List<GetHierarchyLevelListDto>? Data { get; init; }
}

public class GetHierarchyLevelWithoutProductionBonusInfoListRequest : IRequest<GetHierarchyLevelWithoutProductionBonusInfoListResult>
{
    public bool IsDeleted { get; init; } = false;
}

public class GetHierarchyLevelWithoutProductionBonusInfoListHandler : IRequestHandler<GetHierarchyLevelWithoutProductionBonusInfoListRequest, GetHierarchyLevelWithoutProductionBonusInfoListResult>
{
    private readonly IMapper _mapper;
    private readonly IQueryContext _context;

    public GetHierarchyLevelWithoutProductionBonusInfoListHandler(IMapper mapper, IQueryContext context)
    {
        _mapper = mapper;
        _context = context;
    }

    public async Task<GetHierarchyLevelWithoutProductionBonusInfoListResult> Handle(GetHierarchyLevelWithoutProductionBonusInfoListRequest request, CancellationToken cancellationToken)
    {
        // Get HierarchyLevels that DON'T have any ProductionBonusInfo records
        var query = _context
            .HierarchyLevel
            .AsNoTracking()
            .IsDeletedEqualTo(request.IsDeleted)
            .Where(h => !_context.SalesmanProductionBonusInfo
                .Any(p => p.HierarchyLevelId == h.Id && !p.IsDeleted))
            .AsQueryable();

        var entities = await query.ToListAsync(cancellationToken);

        var dtos = _mapper.Map<List<GetHierarchyLevelListDto>>(entities);

        return new GetHierarchyLevelWithoutProductionBonusInfoListResult
        {
            Data = dtos
        };
    }
} 