using Application.Common.CQS.Queries;
using Application.Common.Extensions;
using AutoMapper;
using Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.HierarchyLevelManager.Queries;

public class GetHierarchyLevelWithProductionBonusInfoListResult
{
    public List<GetHierarchyLevelListDto>? Data { get; init; }
}

public class GetHierarchyLevelWithProductionBonusInfoListRequest : IRequest<GetHierarchyLevelWithProductionBonusInfoListResult>
{
    public bool IsDeleted { get; init; } = false;
}

public class GetHierarchyLevelWithProductionBonusInfoListHandler : IRequestHandler<GetHierarchyLevelWithProductionBonusInfoListRequest, GetHierarchyLevelWithProductionBonusInfoListResult>
{
    private readonly IMapper _mapper;
    private readonly IQueryContext _context;

    public GetHierarchyLevelWithProductionBonusInfoListHandler(IMapper mapper, IQueryContext context)
    {
        _mapper = mapper;
        _context = context;
    }

    public async Task<GetHierarchyLevelWithProductionBonusInfoListResult> Handle(GetHierarchyLevelWithProductionBonusInfoListRequest request, CancellationToken cancellationToken)
    {
        // Get HierarchyLevels that have at least one ProductionBonusInfo record
        var query = _context
            .HierarchyLevel
            .AsNoTracking()
            .IsDeletedEqualTo(request.IsDeleted)
            .Where(h => _context.SalesmanProductionBonusInfo
                .Any(p => p.HierarchyLevelId == h.Id && !p.IsDeleted))
            .AsQueryable();

        var entities = await query.ToListAsync(cancellationToken);

        var dtos = _mapper.Map<List<GetHierarchyLevelListDto>>(entities);

        return new GetHierarchyLevelWithProductionBonusInfoListResult
        {
            Data = dtos
        };
    }
} 