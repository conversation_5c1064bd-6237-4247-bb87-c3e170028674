using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;

namespace Application.Features.HierarchyLevelManager.Commands;

public class UpdateHierarchyLevelResult
{
    public HierarchyLevel? Data { get; set; }
}

public class UpdateHierarchyLevelRequest : IRequest<UpdateHierarchyLevelResult>
{
    public string? Id { get; init; }
    public string? LevelCode { get; init; }
    public string? Position { get; init; }
    public decimal CommissionRate { get; init; }
    public decimal OverridingRate { get; init; }
    public decimal ReserveRate { get; init; }
    public decimal ReserveCap { get; init; }
    public string? UpdatedById { get; init; }
}

public class UpdateHierarchyLevelValidator : AbstractValidator<UpdateHierarchyLevelRequest>
{
    public UpdateHierarchyLevelValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
        RuleFor(x => x.LevelCode).NotEmpty();
        RuleFor(x => x.Position).NotEmpty();
        RuleFor(x => x.CommissionRate).GreaterThanOrEqualTo(0);
        RuleFor(x => x.OverridingRate).GreaterThanOrEqualTo(0);
        RuleFor(x => x.ReserveRate).GreaterThanOrEqualTo(0);
        RuleFor(x => x.ReserveCap).GreaterThanOrEqualTo(0);
    }
}

public class UpdateHierarchyLevelHandler : IRequestHandler<UpdateHierarchyLevelRequest, UpdateHierarchyLevelResult>
{
    private readonly ICommandRepository<HierarchyLevel> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public UpdateHierarchyLevelHandler(
        ICommandRepository<HierarchyLevel> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<UpdateHierarchyLevelResult> Handle(UpdateHierarchyLevelRequest request, CancellationToken cancellationToken)
    {
        var entity = await _repository.GetAsync(request.Id ?? string.Empty, cancellationToken);

        if (entity == null)
        {
            throw new Exception($"Entity not found: {request.Id}");
        }

        entity.UpdatedById = request.UpdatedById;

        entity.LevelCode = request.LevelCode;
        entity.Position = request.Position;
        entity.CommissionRate = request.CommissionRate;
        entity.OverridingRate = request.OverridingRate;
        entity.ReserveRate = request.ReserveRate;
        entity.ReserveCap = request.ReserveCap;

        _repository.Update(entity);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new UpdateHierarchyLevelResult
        {
            Data = entity
        };
    }
} 