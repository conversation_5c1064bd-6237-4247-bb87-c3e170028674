using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.HierarchyLevelManager.Commands;

public class CreateHierarchyLevelResult
{
    public HierarchyLevel? Data { get; set; }
}

public class CreateHierarchyLevelRequest : IRequest<CreateHierarchyLevelResult>
{
    public string? LevelCode { get; init; }
    public string? Position { get; init; }
    public decimal? CommissionRate { get; init; }
    public decimal? OverridingRate { get; init; }
    public decimal? ReserveRate { get; init; }
    public decimal? ReserveCap { get; init; }
    public string? CreatedById { get; init; }
}

public class CreateHierarchyLevelValidator : AbstractValidator<CreateHierarchyLevelRequest>
{
    public CreateHierarchyLevelValidator()
    {
        RuleFor(x => x.LevelCode).NotEmpty();
        RuleFor(x => x.Position).NotEmpty();
        RuleFor(x => x.CommissionRate).GreaterThanOrEqualTo(0);
        RuleFor(x => x.OverridingRate).GreaterThanOrEqualTo(0);
        RuleFor(x => x.ReserveRate).GreaterThanOrEqualTo(0);
        RuleFor(x => x.ReserveCap).GreaterThanOrEqualTo(0);
    }
}

public class CreateHierarchyLevelHandler : IRequestHandler<CreateHierarchyLevelRequest, CreateHierarchyLevelResult>
{
    private readonly ICommandRepository<HierarchyLevel> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public CreateHierarchyLevelHandler(
        ICommandRepository<HierarchyLevel> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<CreateHierarchyLevelResult> Handle(CreateHierarchyLevelRequest request, CancellationToken cancellationToken = default)
    {
        // Check for duplicate LevelCode
        var existingLevel = await _repository.GetQuery()
            .Where(x => x.LevelCode == request.LevelCode && !x.IsDeleted)
            .FirstOrDefaultAsync(cancellationToken);

        if (existingLevel != null)
        {
            throw new Exception($"Level Code :'{request.LevelCode}' already exists.");
        }

        var entity = new HierarchyLevel();
        entity.CreatedById = request.CreatedById;

        entity.LevelCode = request.LevelCode;
        entity.Position = request.Position;
        entity.CommissionRate = request.CommissionRate;
        entity.OverridingRate = request.OverridingRate;
        entity.ReserveRate = request.ReserveRate;
        entity.ReserveCap = request.ReserveCap;

        await _repository.CreateAsync(entity, cancellationToken);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new CreateHierarchyLevelResult
        {
            Data = entity
        };
    }
} 