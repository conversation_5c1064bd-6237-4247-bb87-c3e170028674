using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.HierarchyLevelManager.Commands;

public class DeleteHierarchyLevelResult
{
    public HierarchyLevel? Data { get; set; }
}

public class DeleteHierarchyLevelRequest : IRequest<DeleteHierarchyLevelResult>
{
    public string? Id { get; init; }
    public string? DeletedById { get; init; }
}

public class DeleteHierarchyLevelValidator : AbstractValidator<DeleteHierarchyLevelRequest>
{
    public DeleteHierarchyLevelValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
    }
}

public class DeleteHierarchyLevelHandler : IRequestHandler<DeleteHierarchyLevelRequest, DeleteHierarchyLevelResult>
{
    private readonly ICommandRepository<HierarchyLevel> _repository;
    private readonly ICommandRepository<SalesmanProductionBonusInfo> _bonusInfoRepository;
    private readonly IUnitOfWork _unitOfWork;

    public DeleteHierarchyLevelHandler(
        ICommandRepository<HierarchyLevel> repository,
        ICommandRepository<SalesmanProductionBonusInfo> bonusInfoRepository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _bonusInfoRepository = bonusInfoRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task<DeleteHierarchyLevelResult> Handle(DeleteHierarchyLevelRequest request, CancellationToken cancellationToken)
    {
        var entity = await _repository.GetAsync(request.Id ?? string.Empty, cancellationToken);

        if (entity == null)
        {
            throw new Exception($"Entity not found: {request.Id}");
        }

        // Check if HierarchyLevel ID is being used in SalesmanProductionBonusInfo
        var isHierarchyLevelInUse = await _bonusInfoRepository
            .GetQuery()
            .Where(x => !x.IsDeleted && x.HierarchyLevelId == entity.Id)
            .AnyAsync(cancellationToken);

        if (isHierarchyLevelInUse)
        {
            throw new Exception($"Cannot delete Hierarchy Level '{entity.LevelCode} - {entity.Position}' because it is being used in Salesman Production Bonus Info records.");
        }

        entity.UpdatedById = request.DeletedById;

        _repository.Delete(entity);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new DeleteHierarchyLevelResult
        {
            Data = entity
        };
    }
} 