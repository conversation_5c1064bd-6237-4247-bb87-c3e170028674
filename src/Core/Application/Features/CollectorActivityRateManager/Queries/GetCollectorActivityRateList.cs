﻿using Application.Common.CQS.Queries;
using Application.Common.Extensions;
using AutoMapper;
using Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.CollectorActivityRateManager.Queries;

public record GetCollectorActivityRateListDto
{
    public string? Id { get; init; }
    public decimal? ActivityRate { get; init; }
    public decimal? LocalRate { get; init; }
    public decimal? OutstationRate { get; init; }
    public DateTime? CreatedAtUtc { get; init; }
}

public class GetCollectorActivityRateListProfile : Profile
{
    public GetCollectorActivityRateListProfile()
    {
        CreateMap<CollectorActivityRate, GetCollectorActivityRateListDto>();
    }
}

public class GetCollectorActivityRateListResult
{
    public List<GetCollectorActivityRateListDto>? Data { get; init; }
}

public class GetCollectorActivityRateListRequest : IRequest<GetCollectorActivityRateListResult>
{
    public bool IsDeleted { get; init; } = false;
}


public class GetCollectorActivityRateListHandler : IRequestHandler<GetCollectorActivityRateListRequest, GetCollectorActivityRateListResult>
{
    private readonly IMapper _mapper;
    private readonly IQueryContext _context;

    public GetCollectorActivityRateListHandler(IMapper mapper, IQueryContext context)
    {
        _mapper = mapper;
        _context = context;
    }

    public async Task<GetCollectorActivityRateListResult> Handle(GetCollectorActivityRateListRequest request, CancellationToken cancellationToken)
    {
        var query = _context
            .CollectorActivityRate
            .AsNoTracking()
            .IsDeletedEqualTo(request.IsDeleted)
            .AsQueryable();

        var entities = await query.ToListAsync(cancellationToken);

        var dtos = _mapper.Map<List<GetCollectorActivityRateListDto>>(entities);

        return new GetCollectorActivityRateListResult
        {
            Data = dtos
        };
    }


}



