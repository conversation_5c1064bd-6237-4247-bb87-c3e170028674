﻿using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;

namespace Application.Features.CollectorActivityRateManager.Commands;

public class UpdateCollectorActivityRateResult
{
    public CollectorActivityRate? Data { get; set; }
}

public class UpdateCollectorActivityRateRequest : IRequest<UpdateCollectorActivityRateResult>
{
    public string? Id { get; init; }
    public decimal? ActivityRate { get; init; }
    public decimal? LocalRate { get; init; }
    public decimal? OutstationRate { get; init; }
    public string? UpdatedById { get; init; }
}

public class UpdateCollectorActivityRateValidator : AbstractValidator<UpdateCollectorActivityRateRequest>
{
    public UpdateCollectorActivityRateValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
        RuleFor(x => x.ActivityRate).NotEmpty();
        RuleFor(x => x.LocalRate).NotEmpty();
        RuleFor(x => x.OutstationRate).NotEmpty();

    }
}

public class UpdateCollectorActivityRateHandler : IRequestHandler<UpdateCollectorActivityRateRequest, UpdateCollectorActivityRateResult>
{
    private readonly ICommandRepository<CollectorActivityRate> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public UpdateCollectorActivityRateHandler(
        ICommandRepository<CollectorActivityRate> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<UpdateCollectorActivityRateResult> Handle(UpdateCollectorActivityRateRequest request, CancellationToken cancellationToken)
    {

        var entity = await _repository.GetAsync(request.Id ?? string.Empty, cancellationToken);

        if (entity == null)
        {
            throw new Exception($"Entity not found: {request.Id}");
        }

        entity.UpdatedById = request.UpdatedById;

        entity.ActivityRate = request.ActivityRate;
        entity.LocalRate = request.LocalRate;
        entity.OutstationRate = request.OutstationRate;

        _repository.Update(entity);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new UpdateCollectorActivityRateResult
        {
            Data = entity
        };
    }
}

