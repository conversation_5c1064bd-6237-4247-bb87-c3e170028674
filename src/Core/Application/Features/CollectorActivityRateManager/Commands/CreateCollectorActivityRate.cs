﻿using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;


namespace Application.Features.CollectorActivityRateManager.Commands;

public class CreateCollectorActivityRateResult
{
    public CollectorActivityRate? Data { get; set; }
}

public class CreateCollectorActivityRateRequest : IRequest<CreateCollectorActivityRateResult>
{
    public decimal? ActivityRate { get; init; }
    public decimal? LocalRate { get; init; }
    public decimal? OutstationRate { get; init; }
    public string? CreatedById { get; init; }
}

public class CreateCollectorActivityRateValidator : AbstractValidator<CreateCollectorActivityRateRequest>
{
    public CreateCollectorActivityRateValidator()
    {
        RuleFor(x => x.ActivityRate).NotEmpty();
        RuleFor(x => x.LocalRate).NotEmpty();
        RuleFor(x => x.OutstationRate).NotEmpty();
    }
}

public class CreateCollectorActivityRateHandler : IRequestHandler<CreateCollectorActivityRateRequest, CreateCollectorActivityRateResult>
{
    private readonly ICommandRepository<CollectorActivityRate> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public CreateCollectorActivityRateHandler(
        ICommandRepository<CollectorActivityRate> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<CreateCollectorActivityRateResult> Handle(CreateCollectorActivityRateRequest request, CancellationToken cancellationToken = default)
    {
        var existingDivision = await _repository.GetQuery()
            .Where(x => x.ActivityRate == request.ActivityRate && !x.IsDeleted)
            .FirstOrDefaultAsync(cancellationToken);

        if (existingDivision != null)
        {
            throw new Exception($"Activity Rate :'{request.ActivityRate}'% already exists.");
        }

        var entity = new CollectorActivityRate();
        entity.CreatedById = request.CreatedById;

        entity.ActivityRate = request.ActivityRate;
        entity.LocalRate = request.LocalRate;
        entity.OutstationRate = request.OutstationRate;

        await _repository.CreateAsync(entity, cancellationToken);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new CreateCollectorActivityRateResult
        {
            Data = entity
        };
    }
}