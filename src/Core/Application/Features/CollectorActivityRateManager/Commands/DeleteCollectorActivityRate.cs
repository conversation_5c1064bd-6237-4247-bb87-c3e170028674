﻿using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;

namespace Application.Features.CollectorActivityRateManager.Commands;

public class DeleteCollectorActivityRateResult
{
    public CollectorActivityRate? Data { get; set; }
}

public class DeleteCollectorActivityRateRequest : IRequest<DeleteCollectorActivityRateResult>
{
    public string? Id { get; init; }
    public string? DeletedById { get; init; }
}

public class DeleteCollectorActivityRateValidator : AbstractValidator<DeleteCollectorActivityRateRequest>
{
    public DeleteCollectorActivityRateValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
    }
}

public class DeleteCollectorActivityRateHandler : IRequestHandler<DeleteCollectorActivityRateRequest, DeleteCollectorActivityRateResult>
{
    private readonly ICommandRepository<CollectorActivityRate> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public DeleteCollectorActivityRateHandler(
        ICommandRepository<CollectorActivityRate> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<DeleteCollectorActivityRateResult> Handle(DeleteCollectorActivityRateRequest request, CancellationToken cancellationToken)
    {

        var entity = await _repository.GetAsync(request.Id ?? string.Empty, cancellationToken);

        if (entity == null)
        {
            throw new Exception($"Entity not found: {request.Id}");
        }

        entity.UpdatedById = request.DeletedById;

        _repository.Delete(entity);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new DeleteCollectorActivityRateResult
        {
            Data = entity
        };
    }
}

