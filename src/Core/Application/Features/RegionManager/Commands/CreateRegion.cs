﻿using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.RegionManager.Commands;

public class CreateRegionResult
{
    public Region? Data { get; set; }
}

public class CreateRegionRequest : IRequest<CreateRegionResult>
{
    public string? RegionCode { get; init; }
    public string? AreaCode { get; init; }
    public string? Description { get; init; }
    public string? CreatedById { get; init; }
}

public class CreateRegionValidator : AbstractValidator<CreateRegionRequest>
{
    public CreateRegionValidator()
    {
        RuleFor(x => x.RegionCode).NotEmpty();
        RuleFor(x => x.AreaCode).NotEmpty();
        RuleFor(x => x.Description).NotEmpty();
    }
}

public class CreateRegionHandler : IRequestHandler<CreateRegionRequest, CreateRegionResult>
{
    private readonly ICommandRepository<Region> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public CreateRegionHandler(
        ICommandRepository<Region> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<CreateRegionResult> Handle(CreateRegionRequest request, CancellationToken cancellationToken = default)
    {
        var existingDivision = await _repository.GetQuery()
            .Where(x => x.RegionCode == request.RegionCode && !x.IsDeleted)
            .FirstOrDefaultAsync(cancellationToken);

        if (existingDivision != null)
        {
            throw new Exception($"Region Code :'{request.RegionCode}' already exists.");
        }
        
        var entity = new Region();
        entity.CreatedById = request.CreatedById;

        entity.RegionCode = request.RegionCode;
        entity.AreaCode = request.AreaCode;
        entity.Description = request.Description;

        await _repository.CreateAsync(entity, cancellationToken);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new CreateRegionResult
        {
            Data = entity
        };
    }
}