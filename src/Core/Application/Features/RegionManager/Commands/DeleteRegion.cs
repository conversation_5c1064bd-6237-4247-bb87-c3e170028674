﻿using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.RegionManager.Commands;

public class DeleteRegionResult
{
    public Region? Data { get; set; }
}

public class DeleteRegionRequest : IRequest<DeleteRegionResult>
{
    public string? Id { get; init; }
    public string? DeletedById { get; init; }
}

public class DeleteRegionValidator : AbstractValidator<DeleteRegionRequest>
{
    public DeleteRegionValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
    }
}

public class DeleteRegionHandler : IRequestHandler<DeleteRegionRequest, DeleteRegionResult>
{
    private readonly ICommandRepository<Region> _repository;
    private readonly ICommandRepository<SalesmanRallyBonus> _rallyBonusRepository;
    private readonly IUnitOfWork _unitOfWork;

    public DeleteRegionHandler(
        ICommandRepository<Region> repository,
        ICommandRepository<SalesmanRallyBonus> rallyBonusRepository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _rallyBonusRepository = rallyBonusRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task<DeleteRegionResult> Handle(DeleteRegionRequest request, CancellationToken cancellationToken)
    {
        var entity = await _repository.GetAsync(request.Id ?? string.Empty, cancellationToken);

        if (entity == null)
        {
            throw new Exception($"Entity not found: {request.Id}");
        }

        // Check if Region ID is being used in SalesmanRallyBonus
        var isRegionInUse = await _rallyBonusRepository
            .GetQuery()
            .Where(x => !x.IsDeleted && x.RegionId == entity.Id)
            .AnyAsync(cancellationToken);

        if (isRegionInUse)
        {
            throw new Exception($"Cannot delete Region '{entity.RegionCode} - {entity.Description}' because it is being used in Salesman Rally Bonus records.");
        }

        entity.UpdatedById = request.DeletedById;

        _repository.Delete(entity);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new DeleteRegionResult
        {
            Data = entity
        };
    }
}

