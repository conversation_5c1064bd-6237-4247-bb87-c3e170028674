﻿using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;

namespace Application.Features.RegionManager.Commands;

public class UpdateRegionResult
{
    public Region? Data { get; set; }
}

public class UpdateRegionRequest : IRequest<UpdateRegionResult>
{
    public string? Id { get; init; }
    public string? RegionCode { get; init; }
    public string? AreaCode { get; init; }
    public string? Description { get; init; }
    public string? UpdatedById { get; init; }
}

public class UpdateRegionValidator : AbstractValidator<UpdateRegionRequest>
{
    public UpdateRegionValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
        RuleFor(x => x.RegionCode).NotEmpty();
        RuleFor(x => x.AreaCode).NotEmpty();
        RuleFor(x => x.Description).NotEmpty();

    }
}

public class UpdateRegionHandler : IRequestHandler<UpdateRegionRequest, UpdateRegionResult>
{
    private readonly ICommandRepository<Region> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public UpdateRegionHandler(
        ICommandRepository<Region> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<UpdateRegionResult> Handle(UpdateRegionRequest request, CancellationToken cancellationToken)
    {

        var entity = await _repository.GetAsync(request.Id ?? string.Empty, cancellationToken);

        if (entity == null)
        {
            throw new Exception($"Entity not found: {request.Id}");
        }

        entity.UpdatedById = request.UpdatedById;

        entity.RegionCode = request.RegionCode;
        entity.AreaCode = request.AreaCode;
        entity.Description = request.Description;

        _repository.Update(entity);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new UpdateRegionResult
        {
            Data = entity
        };
    }
}

