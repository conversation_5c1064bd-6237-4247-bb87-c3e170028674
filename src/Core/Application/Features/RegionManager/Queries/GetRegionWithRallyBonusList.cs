using Application.Common.CQS.Queries;
using Application.Common.Extensions;
using AutoMapper;
using Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.RegionManager.Queries;

public class GetRegionWithRallyBonusListResult
{
    public List<GetRegionListDto>? Data { get; init; }
}

public class GetRegionWithRallyBonusListRequest : IRequest<GetRegionWithRallyBonusListResult>
{
    public bool IsDeleted { get; init; } = false;
}

public class GetRegionWithRallyBonusListHandler : IRequestHandler<GetRegionWithRallyBonusListRequest, GetRegionWithRallyBonusListResult>
{
    private readonly IMapper _mapper;
    private readonly IQueryContext _context;

    public GetRegionWithRallyBonusListHandler(IMapper mapper, IQueryContext context)
    {
        _mapper = mapper;
        _context = context;
    }

    public async Task<GetRegionWithRallyBonusListResult> Handle(GetRegionWithRallyBonusListRequest request, CancellationToken cancellationToken)
    {
        // Get Regions that have at least one RallyBonus record
        var query = _context
            .Region
            .AsNoTracking()
            .IsDeletedEqualTo(request.IsDeleted)
            .Where(r => _context.SalesmanRallyBonus
                .Any(p => p.RegionId == r.Id && !p.IsDeleted))
            .AsQueryable();

        var entities = await query.ToListAsync(cancellationToken);

        var dtos = _mapper.Map<List<GetRegionListDto>>(entities);

        return new GetRegionWithRallyBonusListResult
        {
            Data = dtos
        };
    }
} 