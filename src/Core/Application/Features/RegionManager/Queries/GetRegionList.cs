﻿using Application.Common.CQS.Queries;
using Application.Common.Extensions;
using AutoMapper;
using Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.RegionManager.Queries;

public record GetRegionListDto
{
    public string? Id { get; init; }
    public string? AreaCode { get; init; }
    public string? RegionCode { get; init; }
    public string? Description { get; init; }
    public DateTime? CreatedAtUtc { get; init; }
}

public class GetRegionListProfile : Profile
{
    public GetRegionListProfile()
    {
        CreateMap<Region, GetRegionListDto>();
    }
}

public class GetRegionListResult
{
    public List<GetRegionListDto>? Data { get; init; }
}

public class GetRegionListRequest : IRequest<GetRegionListResult>
{
    public bool IsDeleted { get; init; } = false;
}


public class GetRegionListHandler : IRequestHandler<GetRegionListRequest, GetRegionListResult>
{
    private readonly IMapper _mapper;
    private readonly IQueryContext _context;

    public GetRegionListHandler(IMapper mapper, IQueryContext context)
    {
        _mapper = mapper;
        _context = context;
    }

    public async Task<GetRegionListResult> Handle(GetRegionListRequest request, CancellationToken cancellationToken)
    {
        var query = _context
            .Region
            .AsNoTracking()
            .IsDeletedEqualTo(request.IsDeleted)
            .AsQueryable();

        var entities = await query.ToListAsync(cancellationToken);

        var dtos = _mapper.Map<List<GetRegionListDto>>(entities);

        return new GetRegionListResult
        {
            Data = dtos
        };
    }


}



