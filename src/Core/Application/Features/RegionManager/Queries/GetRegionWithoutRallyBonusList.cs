using Application.Common.CQS.Queries;
using Application.Common.Extensions;
using AutoMapper;
using Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.RegionManager.Queries;

public class GetRegionWithoutRallyBonusListResult
{
    public List<GetRegionListDto>? Data { get; init; }
}

public class GetRegionWithoutRallyBonusListRequest : IRequest<GetRegionWithoutRallyBonusListResult>
{
    public bool IsDeleted { get; init; } = false;
}

public class GetRegionWithoutRallyBonusListHandler : IRequestHandler<GetRegionWithoutRallyBonusListRequest, GetRegionWithoutRallyBonusListResult>
{
    private readonly IMapper _mapper;
    private readonly IQueryContext _context;

    public GetRegionWithoutRallyBonusListHandler(IMapper mapper, IQueryContext context)
    {
        _mapper = mapper;
        _context = context;
    }

    public async Task<GetRegionWithoutRallyBonusListResult> Handle(GetRegionWithoutRallyBonusListRequest request, CancellationToken cancellationToken)
    {
        // Get Regions that DON'T have any RallyBonus records
        var query = _context
            .Region
            .AsNoTracking()
            .IsDeletedEqualTo(request.IsDeleted)
            .Where(r => !_context.SalesmanRallyBonus
                .Any(p => p.RegionId == r.Id && !p.IsDeleted))
            .AsQueryable();

        var entities = await query.ToListAsync(cancellationToken);

        var dtos = _mapper.Map<List<GetRegionListDto>>(entities);

        return new GetRegionWithoutRallyBonusListResult
        {
            Data = dtos
        };
    }
} 