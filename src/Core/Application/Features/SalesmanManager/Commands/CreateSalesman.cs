using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.SalesmanManager.Commands;

public class CreateSalesmanResult
{
    public Salesman? Data { get; set; }

}

public class CreateSalesmanRequest : IRequest<CreateSalesmanResult>
{
    public string? SalesmanCode { get; init; }
    public bool IsActive { get; set; } = true;
    public bool IsCustomCommissionRate { get; set; } = false;
    public string? Name { get; set; }
    public string? Gender { get; set; }
    public string? Address1 { get; set; }
    public string? Address2 { get; set; }
    public string? City { get; set; }
    public string? State { get; set; }
    public string? PostCode { get; set; }
    public string? NricNo { get; set; }
    public string? Race { get; set; }
    public string? PhoneNo { get; set; }
    public DateTime? DateJoined { get; set; }
    public string? BankName { get; set; }
    public string? BankAccountNo { get; set; }
    public string? TinNo { get; set; }
    public string? Remarks { get; set; }
    public string? LevelCode { get; set; }
    public string? Position { get; set; }
    public string? DivisionCode { get; set; }
    public string? DivisionMgr { get; set; }
    public string? ParentCode { get; set; }
    public bool IsDM { get; set; } = true;
    public decimal? CommissionRate { get; set; }
    public decimal? OverridingRate { get; set; }
    public decimal? ReserveRate { get; set; }
    public decimal? ReserveCap { get; set; }
    public string? CreatedById { get; init; }

}

public class CreateSalesmanValidator : AbstractValidator<CreateSalesmanRequest>
{
    public CreateSalesmanValidator()
    {
        RuleFor(x => x.SalesmanCode).NotEmpty();
        RuleFor(x => x.IsActive).NotNull();
        RuleFor(x => x.IsCustomCommissionRate).NotNull();
        RuleFor(x => x.Name).NotEmpty();
        RuleFor(x => x.Gender).NotEmpty();
        RuleFor(x => x.Address1).NotEmpty();
        RuleFor(x => x.State).NotEmpty();
        RuleFor(x => x.City).NotEmpty();
        RuleFor(x => x.PostCode).NotEmpty();
        RuleFor(x => x.NricNo).NotEmpty();
        RuleFor(x => x.Race).NotEmpty();
        RuleFor(x => x.PhoneNo).NotEmpty();
        RuleFor(x => x.DateJoined).NotEmpty();
        RuleFor(x => x.BankName).NotEmpty();
        RuleFor(x => x.BankAccountNo).NotEmpty();
        RuleFor(x => x.TinNo).NotEmpty();
        RuleFor(x => x.Remarks).NotEmpty();
        RuleFor(x => x.LevelCode).NotEmpty();
        RuleFor(x => x.Position).NotEmpty();
        RuleFor(x => x.DivisionCode).NotEmpty();
        RuleFor(x => x.DivisionMgr).NotEmpty();
        RuleFor(x => x.ParentCode).NotEmpty();
        RuleFor(x => x.IsDM).NotNull();
        RuleFor(x => x.CommissionRate).NotEmpty();
        RuleFor(x => x.OverridingRate).NotEmpty();
        RuleFor(x => x.ReserveRate).NotEmpty();
        RuleFor(x => x.ReserveCap).NotEmpty();

    }
}

public class CreateSalesmanHandler : IRequestHandler<CreateSalesmanRequest, CreateSalesmanResult>
{
    private readonly ICommandRepository<Salesman> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public CreateSalesmanHandler(
        ICommandRepository<Salesman> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<CreateSalesmanResult> Handle(CreateSalesmanRequest request, CancellationToken cancellationToken = default)
    {
        var codeExists = await _repository.GetQuery()
            .AnyAsync(x => !x.IsDeleted && x.SalesmanCode == request.SalesmanCode, cancellationToken);

        if (codeExists)
            throw new Exception($"Salesman code '{request.SalesmanCode}' already exists.");

        var nricExists = await _repository.GetQuery()
            .AnyAsync(x => !x.IsDeleted && x.NricNo == request.NricNo, cancellationToken);

        if (nricExists)
            throw new Exception($"NRIC No '{request.NricNo}' is already taken by another user.");

        var entity = new Salesman();
        entity.CreatedById = request.CreatedById;

        entity.SalesmanCode = request.SalesmanCode;
        entity.IsActive = request.IsActive;
        entity.IsCustomCommissionRate = request.IsCustomCommissionRate;
        entity.Name = request.Name;
        entity.Gender = request.Gender;
        entity.Address1 = request.Address1;
        entity.Address2 = request.Address2;
        entity.State = request.State;
        entity.City = request.City;
        entity.PostCode = request.PostCode;
        entity.NricNo = request.NricNo;
        entity.Race = request.Race;
        entity.PhoneNo = request.PhoneNo;
        entity.DateJoined = request.DateJoined;
        entity.BankName = request.BankName;
        entity.BankAccountNo = request.BankAccountNo;
        entity.TinNo = request.TinNo;
        entity.Remarks = request.Remarks;
        entity.LevelCode = request.LevelCode;
        entity.Position = request.Position;
        entity.DivisionCode = request.DivisionCode;
        entity.DivisionMgr = request.DivisionMgr;
        entity.ParentCode = request.ParentCode;
        entity.IsDM = request.IsDM;
        entity.CommissionRate = request.CommissionRate;
        entity.OverridingRate = request.OverridingRate;
        entity.ReserveRate = request.ReserveRate;
        entity.ReserveCap = request.ReserveCap;


        await _repository.CreateAsync(entity, cancellationToken);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new CreateSalesmanResult
        {
            Data = entity
        };
    }
}