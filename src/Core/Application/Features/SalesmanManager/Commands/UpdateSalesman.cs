﻿using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;

namespace Application.Features.SalesmanManager.Commands;

public class UpdateSalesmanResult
{
    public Salesman? Data { get; set; }
}

public class UpdateSalesmanRequest : IRequest<UpdateSalesmanResult>
{
    public string? Id { get; init; }
    public string? SalesmanCode { get; init; }
    public bool IsActive { get; set; } = true;
    public bool IsCustomCommissionRate { get; set; } = false;
    public string? Name { get; set; }
    public string? Gender { get; set; }
    public string? Address1 { get; set; }
    public string? Address2 { get; set; }
    public string? City { get; set; }
    public string? State { get; set; }
    public string? PostCode { get; set; }
    public string? NricNo { get; set; }
    public string? Race { get; set; }
    public string? PhoneNo { get; set; }
    public DateTime? DateJoined { get; set; }
    public string? BankName { get; set; }
    public string? BankAccountNo { get; set; }
    public string? TinNo { get; set; }        
    public string? Remarks { get; set; }
    public string? LevelCode { get; set; }
    public string? Position { get; set; }
    public string? DivisionCode { get; set; }
    public string? DivisionMgr { get; set; }
    public string? ParentCode { get; set; }
    public bool IsDM { get; set; } = true;
    public decimal? CommissionRate { get; set; }
    public decimal? OverridingRate { get; set; }
    public decimal? ReserveRate { get; set; }
    public decimal? ReserveCap { get; set; }
    public string? UpdatedById { get; init; }

    
}

public class UpdateSalesmanValidator : AbstractValidator<UpdateSalesmanRequest>
{
    public UpdateSalesmanValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
        RuleFor(x => x.SalesmanCode).NotEmpty();
        RuleFor(x => x.IsActive).NotNull();
        RuleFor(x => x.IsCustomCommissionRate).NotNull();
        RuleFor(x => x.Name).NotEmpty();
        RuleFor(x => x.Gender).NotEmpty();
        RuleFor(x => x.Address1).NotEmpty();
        RuleFor(x => x.State).NotEmpty();
        RuleFor(x => x.City).NotEmpty();
        RuleFor(x => x.PostCode).NotEmpty();
        RuleFor(x => x.NricNo).NotEmpty();
        RuleFor(x => x.Race).NotEmpty();
        RuleFor(x => x.PhoneNo).NotEmpty();
        RuleFor(x => x.DateJoined).NotEmpty();
        RuleFor(x => x.BankName).NotEmpty();
        RuleFor(x => x.BankAccountNo).NotEmpty();
        RuleFor(x => x.TinNo).NotEmpty();
        RuleFor(x => x.Remarks).NotEmpty();
        RuleFor(x => x.LevelCode).NotEmpty();
        RuleFor(x => x.Position).NotEmpty();
        RuleFor(x => x.DivisionCode).NotEmpty();
        RuleFor(x => x.DivisionMgr).NotEmpty();
        RuleFor(x => x.ParentCode).NotEmpty();
        RuleFor(x => x.IsDM).NotNull();
        RuleFor(x => x.CommissionRate).NotEmpty();
        RuleFor(x => x.OverridingRate).NotEmpty();
        RuleFor(x => x.ReserveRate).NotEmpty();
        RuleFor(x => x.ReserveCap).NotEmpty();
        
    }
}

public class UpdateSalesmanHandler : IRequestHandler<UpdateSalesmanRequest, UpdateSalesmanResult>
{
    private readonly ICommandRepository<Salesman> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public UpdateSalesmanHandler(
        ICommandRepository<Salesman> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<UpdateSalesmanResult> Handle(UpdateSalesmanRequest request, CancellationToken cancellationToken)
    {
        var entity = await _repository.GetAsync(request.Id ?? string.Empty, cancellationToken);

        if (entity == null)
        {
            throw new Exception($"Entity not found: {request.Id}");
        }

        entity.UpdatedById = request.UpdatedById;

        entity.SalesmanCode = request.SalesmanCode;
        entity.IsActive = request.IsActive;
        entity.IsCustomCommissionRate = request.IsCustomCommissionRate;
        entity.Name = request.Name;
        entity.Gender = request.Gender;
        entity.Address1 = request.Address1;
        entity.Address2 = request.Address2;
        entity.State = request.State;
        entity.City = request.City;
        entity.PostCode = request.PostCode;
        entity.NricNo = request.NricNo;
        entity.Race = request.Race;
        entity.PhoneNo = request.PhoneNo;
        entity.DateJoined = request.DateJoined;
        entity.BankName = request.BankName;
        entity.BankAccountNo = request.BankAccountNo;
        entity.TinNo = request.TinNo;
        entity.Remarks = request.Remarks;
        entity.LevelCode = request.LevelCode;
        entity.Position = request.Position;
        entity.DivisionCode = request.DivisionCode;
        entity.DivisionMgr = request.DivisionMgr;
        entity.ParentCode = request.ParentCode;
        entity.IsDM = request.IsDM;
        entity.CommissionRate = request.CommissionRate;
        entity.OverridingRate = request.OverridingRate;
        entity.ReserveRate = request.ReserveRate;
        entity.ReserveCap = request.ReserveCap;

        _repository.Update(entity);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new UpdateSalesmanResult
        {
            Data = entity
        };
    }
}

