﻿using Application.Common.CQS.Queries;
using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.SalesmanManager.Commands;

public class DeleteSalesmanResult
{
    public Salesman? Data { get; set; }
    public bool HasDownlines { get; set; }
    public int DownlinesCount { get; set; }
    public List<string> DeletedDownlineCodes { get; set; } = new List<string>();
}

public class DeleteSalesmanRequest : IRequest<DeleteSalesmanResult>
{
    public string? Id { get; init; }
    public string? DeletedById { get; init; }

    // This property name must match exactly what's sent from the frontend
    public bool deleteWithDownlines { get; init; } = false;
}

public class DeleteSalesmanValidator : AbstractValidator<DeleteSalesmanRequest>
{
    public DeleteSalesmanValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
    }
}

public class DeleteSalesmanHandler : IRequestHandler<DeleteSalesmanRequest, DeleteSalesmanResult>
{
    private readonly ICommandRepository<Salesman> _repository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IQueryContext _queryContext;

    public DeleteSalesmanHandler(
        ICommandRepository<Salesman> repository,
        IUnitOfWork unitOfWork,
        IQueryContext queryContext
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
        _queryContext = queryContext;
    }

    public async Task<DeleteSalesmanResult> Handle(DeleteSalesmanRequest request, CancellationToken cancellationToken)
    {
        // Log the incoming request
        Console.WriteLine($"DeleteSalesman request received: Id={request.Id}, deleteWithDownlines={request.deleteWithDownlines}");

        var entity = await _repository.GetAsync(request.Id ?? string.Empty, cancellationToken);

        if (entity == null)
        {
            throw new Exception($"Entity not found: {request.Id}");
        }

        Console.WriteLine($"Checking if salesman {entity.SalesmanCode} has downlines");

        // First, just check if there are any direct downlines (faster check)
        var hasDirectDownlines = await _queryContext.Salesman
            .AsNoTracking()
            .AnyAsync(s => s.ParentCode == entity.SalesmanCode && !s.IsDeleted && !s.IsDM, cancellationToken);

        if (!hasDirectDownlines)
        {
            Console.WriteLine($"No downlines found for {entity.SalesmanCode}, proceeding with deletion");

            // No downlines, proceed with deletion
            entity.UpdatedById = request.DeletedById;
            _repository.Delete(entity);
            await _unitOfWork.SaveAsync(cancellationToken);

            return new DeleteSalesmanResult
            {
                Data = entity,
                HasDownlines = false,
                DownlinesCount = 0,
                DeletedDownlineCodes = new List<string>()
            };
        }

        Console.WriteLine($"Salesman {entity.SalesmanCode} has downlines");

        // If not confirmed to delete with downlines, return information without deleting
        if (!request.deleteWithDownlines)
        {
            Console.WriteLine($"Returning without deleting - deleteWithDownlines={request.deleteWithDownlines}");

            // Get downline count for information purposes
            var downlinesCount = GetAllDownlineIds(entity.SalesmanCode, cancellationToken).Result.Count;

            var result = new DeleteSalesmanResult
            {
                Data = entity,
                HasDownlines = true,
                DownlinesCount = downlinesCount,
                DeletedDownlineCodes = new List<string>()
            };

            Console.WriteLine($"Returning result: HasDownlines={result.HasDownlines}, DownlinesCount={result.DownlinesCount}");
            return result;
        }

        // At this point, the user has confirmed deletion with downlines
        Console.WriteLine($"User confirmed deletion with downlines for {entity.SalesmanCode}");

        // Get all downline IDs in a single query for deletion
        var downlineIds = await GetAllDownlineIds(entity.SalesmanCode, cancellationToken);
        var deletedDownlineCodes = new List<string>();

        Console.WriteLine($"Deleting {downlineIds.Count} downlines for salesman {entity.SalesmanCode}");

        // Delete all downlines by ID to avoid tracking issues
        foreach (var downlineId in downlineIds)
        {
            // Get each entity individually to avoid tracking conflicts
            var downline = await _repository.GetAsync(downlineId, cancellationToken);
            if (downline != null)
            {
                downline.UpdatedById = request.DeletedById;
                _repository.Delete(downline);
                deletedDownlineCodes.Add(downline.SalesmanCode ?? string.Empty);
            }
        }

        // Delete the salesman
        entity.UpdatedById = request.DeletedById;
        _repository.Delete(entity);

        Console.WriteLine($"Deleted salesman {entity.SalesmanCode} and {downlineIds.Count} downlines");

        // Save all changes
        await _unitOfWork.SaveAsync(cancellationToken);

        return new DeleteSalesmanResult
        {
            Data = entity,
            HasDownlines = true,
            DownlinesCount = downlineIds.Count,
            DeletedDownlineCodes = deletedDownlineCodes
        };
    }

    // Optimized method to get all downline IDs to avoid entity tracking issues
    private async Task<List<string>> GetAllDownlineIds(string? salesmanCode, CancellationToken cancellationToken)
    {
        Console.WriteLine($"GetAllDownlineIds started for salesmanCode: {salesmanCode}");

        if (string.IsNullOrEmpty(salesmanCode))
        {
            return new List<string>();
        }

        try
        {
            // First, just check if there are any direct downlines (faster check)
            var hasDirectDownlines = await _queryContext.Salesman
                .AsNoTracking()
                .AnyAsync(s => s.ParentCode == salesmanCode && !s.IsDeleted && !s.IsDM, cancellationToken);

            if (!hasDirectDownlines)
            {
                Console.WriteLine($"No direct downlines found for {salesmanCode}");
                return new List<string>();
            }

            Console.WriteLine($"Direct downlines found for {salesmanCode}, fetching all downlines");

            // Get all salesmen codes and parent codes in a single query
            var allSalesmenInfo = await _queryContext.Salesman
                .AsNoTracking()
                .Where(s => !s.IsDeleted && s.SalesmanCode != salesmanCode)
                .Select(s => new { s.Id, s.SalesmanCode, s.ParentCode })
                .ToListAsync(cancellationToken);

            Console.WriteLine($"Fetched {allSalesmenInfo.Count} total salesmen info");

            // Use in-memory processing to build the hierarchy
            var resultIds = new List<string>();
            var processedCodes = new HashSet<string>();
            var codesToProcess = new Queue<string>();

            // Start with the given salesman code
            codesToProcess.Enqueue(salesmanCode);

            // Process the queue
            while (codesToProcess.Count > 0)
            {
                var currentCode = codesToProcess.Dequeue();

                // Skip if already processed to avoid cycles
                if (processedCodes.Contains(currentCode))
                {
                    continue;
                }

                processedCodes.Add(currentCode);

                // Find direct downlines
                var directDownlines = allSalesmenInfo
                    .Where(s => s.ParentCode == currentCode)
                    .ToList();

                // Add IDs to result
                foreach (var downline in directDownlines)
                {
                    resultIds.Add(downline.Id);

                    // Add downline codes to the queue for processing
                    if (downline.SalesmanCode != null)
                    {
                        codesToProcess.Enqueue(downline.SalesmanCode);
                    }
                }
            }

            Console.WriteLine($"Found {resultIds.Count} total downline IDs for {salesmanCode}");
            return resultIds;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in GetAllDownlineIds: {ex.Message}");
            throw;
        }
    }

    // Keep this method for backward compatibility and for cases where we need the full entities
    private async Task<List<Salesman>> GetAllDownlines(string? salesmanCode, CancellationToken cancellationToken)
    {
        Console.WriteLine($"GetAllDownlines started for salesmanCode: {salesmanCode}");

        if (string.IsNullOrEmpty(salesmanCode))
        {
            return new List<Salesman>();
        }

        try
        {
            // First, just check if there are any direct downlines (faster check)
            var hasDirectDownlines = await _queryContext.Salesman
                .AsNoTracking()
                .AnyAsync(s => s.ParentCode == salesmanCode && !s.IsDeleted, cancellationToken);

            if (!hasDirectDownlines)
            {
                Console.WriteLine($"No direct downlines found for {salesmanCode}");
                return new List<Salesman>();
            }

            Console.WriteLine($"Direct downlines found for {salesmanCode}, fetching all downlines");

            // Get all salesmen in a single query
            var allSalesmen = await _queryContext.Salesman
                .AsNoTracking()
                .Where(s => !s.IsDeleted)
                .ToListAsync(cancellationToken);

            Console.WriteLine($"Fetched {allSalesmen.Count} total salesmen");

            // Use in-memory processing to build the hierarchy
            var result = new List<Salesman>();
            var processedCodes = new HashSet<string>();
            var codesToProcess = new Queue<string>();

            // Start with the given salesman code
            codesToProcess.Enqueue(salesmanCode);

            // Process the queue
            while (codesToProcess.Count > 0)
            {
                var currentCode = codesToProcess.Dequeue();

                // Skip if already processed to avoid cycles
                if (processedCodes.Contains(currentCode))
                {
                    continue;
                }

                processedCodes.Add(currentCode);

                // Find direct downlines
                var directDownlines = allSalesmen
                    .Where(s => s.ParentCode == currentCode)
                    .ToList();

                // Add to result
                result.AddRange(directDownlines);

                // Add downline codes to the queue for processing
                foreach (var downline in directDownlines)
                {
                    if (downline.SalesmanCode != null)
                    {
                        codesToProcess.Enqueue(downline.SalesmanCode);
                    }
                }
            }

            Console.WriteLine($"Found {result.Count} total downlines for {salesmanCode}");
            return result;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in GetAllDownlines: {ex.Message}");
            throw;
        }
    }
}

