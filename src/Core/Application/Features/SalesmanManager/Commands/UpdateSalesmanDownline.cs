using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.SalesmanManager.Commands;

public class UpdateSalesmanDownlineResult
{
    public int AffectedRecords { get; set; }
}

public class UpdateSalesmanDownlineRequest : IRequest<UpdateSalesmanDownlineResult>
{
    public string? SalesmanCode { get; init; }
    public string? UpdatedById { get; init; }
}

public class UpdateSalesmanDownlineValidator : AbstractValidator<UpdateSalesmanDownlineRequest>
{
    public UpdateSalesmanDownlineValidator()
    {
        RuleFor(x => x.SalesmanCode).NotEmpty();
        RuleFor(x => x.UpdatedById).NotEmpty();
    }
}

public class UpdateSalesmanDownlineHandler : IRequestHandler<UpdateSalesmanDownlineRequest, UpdateSalesmanDownlineResult>
{
    private readonly ICommandRepository<Salesman> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public UpdateSalesmanDownlineHandler(
        ICommandRepository<Salesman> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<UpdateSalesmanDownlineResult> Handle(UpdateSalesmanDownlineRequest request, CancellationToken cancellationToken)
    {
        // Get the parent salesman to retrieve their commission rate
        var parentSalesman = await _repository.GetQuery()
            .Where(x => x.SalesmanCode == request.SalesmanCode && !x.IsDeleted)
            .FirstOrDefaultAsync(cancellationToken);

        if (parentSalesman == null)
        {
            throw new Exception($"Parent Salesman with code '{request.SalesmanCode}' not found.");
        }

        // Get all salesmen where the parentCode equals the edited salesman's code
        var downlineSalesmen = await _repository.GetQuery()
            .Where(x => x.ParentCode == request.SalesmanCode && !x.IsDeleted)
            .ToListAsync(cancellationToken);

        int affectedRecords = 0;

        foreach (var downline in downlineSalesmen)
        {
            // Calculate new overriding rate
            // Formula: Parent's commission rate - downline's commission rate
            decimal parentCommissionRate = parentSalesman.CommissionRate ?? 0;
            decimal downlineCommissionRate = downline.CommissionRate ?? 0;
            
            // Ensure the difference is not negative
            decimal newOverridingRate = parentCommissionRate - downlineCommissionRate;
            if (newOverridingRate < 0)
            {
                newOverridingRate = 0;
            }
            
            // Only update if the new value is different
            if (downline.OverridingRate != newOverridingRate)
            {
                downline.OverridingRate = newOverridingRate;
                downline.UpdatedById = request.UpdatedById;
                downline.UpdatedAtUtc = DateTime.UtcNow;
                
                _repository.Update(downline);
                affectedRecords++;
            }
        }

        if (affectedRecords > 0)
        {
            await _unitOfWork.SaveAsync(cancellationToken);
        }

        return new UpdateSalesmanDownlineResult
        {
            AffectedRecords = affectedRecords
        };
    }
} 