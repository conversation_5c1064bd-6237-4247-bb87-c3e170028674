﻿using Application.Common.CQS.Queries;
using Application.Common.Extensions;
using AutoMapper;
using Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.SalesmanManager.Queries;

public record GetSalesmanListDto
{
    public string? Id { get; init; }
    public string? SalesmanCode { get; init; }
    public bool IsActive { get; set; } = true;
    public bool IsCustomCommissionRate { get; set; } = false;
    public string? Name { get; set; }
    public string? Gender { get; set; }
    public string? Address1 { get; set; }
    public string? Address2 { get; set; }
    public string? City { get; set; }
    public string? State { get; set; }
    public string? PostCode { get; set; }
    public string? NricNo { get; set; }
    public string? Race { get; set; }
    public string? PhoneNo { get; set; }
    public DateTime? DateJoined { get; set; }
    public string? BankName { get; set; }
    public string? BankAccountNo { get; set; }
    public string? TinNo { get; set; }        
    public string? Remarks { get; set; }
    public string? LevelCode { get; set; }
    public string? Position { get; set; }
    public string? DivisionCode { get; set; }
    public string? DivisionMgr { get; set; }
    public string? ParentCode { get; set; }
    public bool IsDM { get; set; } = true;
    public decimal? CommissionRate { get; set; }
    public decimal? OverridingRate { get; set; }
    public decimal? ReserveRate { get; set; }
    public decimal? ReserveCap { get; set; }
    public DateTime? CreatedAtUtc { get; init; }
}

public class GetSalesmanListProfile : Profile
{
    public GetSalesmanListProfile()
    {
        CreateMap<Salesman, GetSalesmanListDto>();
    }
}

public class GetSalesmanListResult
{
    public List<GetSalesmanListDto>? Data { get; init; }
}

public class GetSalesmanListRequest : IRequest<GetSalesmanListResult>
{
    public bool IsDeleted { get; init; } = false;
}


public class GetSalesmanListHandler : IRequestHandler<GetSalesmanListRequest, GetSalesmanListResult>
{
    private readonly IMapper _mapper;
    private readonly IQueryContext _context;

    public GetSalesmanListHandler(IMapper mapper, IQueryContext context)
    {
        _mapper = mapper;
        _context = context;
    }

    public async Task<GetSalesmanListResult> Handle(GetSalesmanListRequest request, CancellationToken cancellationToken)
    {
        var query = _context
            .Salesman
            .AsNoTracking()
            .IsDeletedEqualTo(request.IsDeleted)
            .AsQueryable();

        var entities = await query.ToListAsync(cancellationToken);

        var dtos = _mapper.Map<List<GetSalesmanListDto>>(entities);

        return new GetSalesmanListResult
        {
            Data = dtos
        };
    }


}



