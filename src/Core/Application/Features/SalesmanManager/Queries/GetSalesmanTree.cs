using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Application.Common.CQS.Queries;
using Application.Common.Extensions;
using Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.SalesmanManager.Queries;
public record SalesmanHierarchy
{
    public string SalesmanCode { get; set; } = null!;
    public string Name { get; set; } = null!;
    public string? ParentCode { get; set; }
    public SalesmanHierarchy? Parent { get; set; }
    public ICollection<SalesmanHierarchy> Children { get; set; } = new List<SalesmanHierarchy>();

}

public class GetSalesmanTreeRequest : IRequest<GetSalesmanTreeResult>
{
    public string? SalesmanCode { get; set; }
    public bool IsDeleted { get; init; } = false;
}

public class GetSalesmanTreeResult
{
    public List<SalesmanHierarchy> SalesmanTree { get; set; } = new();
}

public class GetSalesmanTreeHandler : IRequestHandler<GetSalesmanTreeRequest, GetSalesmanTreeResult>
{
    private readonly IQueryContext _context;

    public GetSalesmanTreeHandler(IQueryContext context)
    {
        _context = context;
    }

    public async Task<GetSalesmanTreeResult> Handle(GetSalesmanTreeRequest request, CancellationToken cancellationToken)
    {

        List<Salesman> query = await _context
         .Salesman
         .AsNoTracking()
         .IsDeletedEqualTo(request.IsDeleted)
         .ToListAsync(cancellationToken);
        string? topSalesmanCode = GetRootSalesmanCodeAsync(query, request.SalesmanCode, cancellationToken);
        SalesmanHierarchy salesmanTree = BuildHierarchy(query, topSalesmanCode, null, new HashSet<string>());

        // Return only the root and its immediate children instead of flattening the entire hierarchy
        var result = new List<SalesmanHierarchy>();
        if (salesmanTree != null)
        {
            result.Add(salesmanTree);
        }
     
        return new GetSalesmanTreeResult { SalesmanTree = result };
    }

    public List<SalesmanHierarchy> FlattenHierarchy(SalesmanHierarchy node)
    {
        var result = new List<SalesmanHierarchy> { node };
        foreach (var child in node.Children)
        {
            result.AddRange(FlattenHierarchy(child));
        }
        return result;
    }
    public string? GetRootSalesmanCodeAsync(List<Salesman> allSalesmen, string salesmanCode, CancellationToken cancellationToken)
{
    while (!string.IsNullOrEmpty(salesmanCode))
    {
        var current = allSalesmen
            .Where(s => s.SalesmanCode == salesmanCode)
            .Select(s => new { s.SalesmanCode, s.ParentCode })
            .FirstOrDefault();

        if (current == null || string.IsNullOrEmpty(current.ParentCode) || current.SalesmanCode == current.ParentCode)
        {
            return current.SalesmanCode;
        }

        // Move up the hierarchy
        salesmanCode = current.ParentCode!;
    }
    return null;
}
    public SalesmanHierarchy? BuildHierarchy(List<Salesman> allSalesmen, string targetSalesmanCode, SalesmanHierarchy? parent = null, HashSet<string> visitedCodes = null)
    {
        // Initialize visited codes if null
        if (visitedCodes == null)
            visitedCodes = new HashSet<string>();
                    
        // Check if this salesman code was already processed to prevent cycles
        if (string.IsNullOrEmpty(targetSalesmanCode) || visitedCodes.Contains(targetSalesmanCode))
            return null;
            
        var current = allSalesmen.FirstOrDefault(s => s.SalesmanCode == targetSalesmanCode);
        if (current == null)
            return null;

        // Add this code to the visited set
        visitedCodes.Add(targetSalesmanCode);

        var node = new SalesmanHierarchy
        {
            SalesmanCode = current.SalesmanCode,
            Name = current.Name,
            ParentCode = current.ParentCode,
            Parent = parent,
            Children = new List<SalesmanHierarchy>()
        };

        var children = allSalesmen
            .Where(s => s.ParentCode == current.SalesmanCode)
            .ToList();

        foreach (var child in children)
        {
            // Pass the current node as parent and share the visited codes set
            var childHierarchy = BuildHierarchy(allSalesmen, child.SalesmanCode, node, visitedCodes);
            if (childHierarchy != null)
                node.Children.Add(childHierarchy);
        }
        return node;
    }
}



