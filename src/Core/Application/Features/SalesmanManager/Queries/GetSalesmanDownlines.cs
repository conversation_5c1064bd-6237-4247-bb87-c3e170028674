using Application.Common.CQS.Queries;
using Application.Common.Extensions;
using Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.SalesmanManager.Queries;

public class GetSalesmanDownlinesResult
{
    public List<Salesman> Data { get; set; } = new List<Salesman>();
}

public class GetSalesmanDownlinesRequest : IRequest<GetSalesmanDownlinesResult>
{
    public string? ParentCode { get; init; }
}

public class GetSalesmanDownlinesHandler : IRequestHandler<GetSalesmanDownlinesRequest, GetSalesmanDownlinesResult>
{
    private readonly IQueryContext _context;

    public GetSalesmanDownlinesHandler(
        IQueryContext context
        )
    {
        _context = context;
    }

    public async Task<GetSalesmanDownlinesResult> Handle(GetSalesmanDownlinesRequest request, CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(request.ParentCode))
        {
            return new GetSalesmanDownlinesResult
            {
                Data = new List<Salesman>()
            };
        }

        var query = _context.Salesman
            .AsNoTracking()
            .IsDeletedEqualTo(false)
            .Where(x => x.ParentCode == request.ParentCode);

        var entities = await query.ToListAsync(cancellationToken);

        return new GetSalesmanDownlinesResult
        {
            Data = entities
        };
    }
} 