using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.PriceListManager.Commands;

public class UpdateCommissionBaseResult
{
    public int UpdatedRecords { get; set; }
}

public class UpdateCommissionBaseRequest : IRequest<UpdateCommissionBaseResult>
{
    public decimal NewCommissionBase { get; init; }
    public string? UpdatedById { get; init; }
}

public class UpdateCommissionBaseValidator : AbstractValidator<UpdateCommissionBaseRequest>
{
    public UpdateCommissionBaseValidator()
    {
        RuleFor(x => x.NewCommissionBase).GreaterThanOrEqualTo(0);
        RuleFor(x => x.UpdatedById).NotEmpty();
    }
}

public class UpdateCommissionBaseHandler : IRequestHandler<UpdateCommissionBaseRequest, UpdateCommissionBaseResult>
{
    private readonly ICommandRepository<PriceList> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public UpdateCommissionBaseHandler(
        ICommandRepository<PriceList> repository,
        IUnitOfWork unitOfWork)
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<UpdateCommissionBaseResult> Handle(UpdateCommissionBaseRequest request, CancellationToken cancellationToken)
    {
        // Get all active PriceList records
        var priceListRecords = await _repository.GetQuery()
            .Where(x => !x.IsDeleted)
            .ToListAsync(cancellationToken);

        int updatedCount = 0;

        // Update commission base for all records
        foreach (var priceList in priceListRecords)
        {
            priceList.CommissionBase = request.NewCommissionBase;
            priceList.UpdatedById = request.UpdatedById;
            
            _repository.Update(priceList);
            updatedCount++;
        }

        await _unitOfWork.SaveAsync(cancellationToken);

        return new UpdateCommissionBaseResult
        {
            UpdatedRecords = updatedCount
        };
    }
}
