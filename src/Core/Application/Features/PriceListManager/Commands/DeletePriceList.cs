﻿using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;

namespace Application.Features.PriceListManager.Commands;

public class DeletePriceListResult
{
    public PriceList? Data { get; set; }
}

public class DeletePriceListRequest : IRequest<DeletePriceListResult>
{
    public string? Id { get; init; }
    public string? DeletedById { get; init; }
}

public class DeletePriceListValidator : AbstractValidator<DeletePriceListRequest>
{
    public DeletePriceListValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
    }
}

public class DeletePriceListHandler : IRequestHandler<DeletePriceListRequest, DeletePriceListResult>
{
    private readonly ICommandRepository<PriceList> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public DeletePriceListHandler(
        ICommandRepository<PriceList> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<DeletePriceListResult> Handle(DeletePriceListRequest request, CancellationToken cancellationToken)
    {

        var entity = await _repository.GetAsync(request.Id ?? string.Empty, cancellationToken);

        if (entity == null)
        {
            throw new Exception($"Entity not found: {request.Id}");
        }

        entity.UpdatedById = request.DeletedById;

        _repository.Delete(entity);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new DeletePriceListResult
        {
            Data = entity
        };
    }
}

