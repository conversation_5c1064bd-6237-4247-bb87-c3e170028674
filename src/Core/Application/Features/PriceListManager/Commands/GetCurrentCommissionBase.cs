using Application.Common.Repositories;
using Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.PriceListManager.Commands;

public class GetCurrentCommissionBaseResult
{
    public decimal? CurrentCommissionBase { get; set; }
}

public class GetCurrentCommissionBaseRequest : IRequest<GetCurrentCommissionBaseResult>
{
}

public class GetCurrentCommissionBaseHandler : IRequestHandler<GetCurrentCommissionBaseRequest, GetCurrentCommissionBaseResult>
{
    private readonly ICommandRepository<PriceList> _repository;

    public GetCurrentCommissionBaseHandler(ICommandRepository<PriceList> repository)
    {
        _repository = repository;
    }

    public async Task<GetCurrentCommissionBaseResult> Handle(GetCurrentCommissionBaseRequest request, CancellationToken cancellationToken)
    {
        // Get the first row of PriceList data to get the commission base reference
        var firstPriceList = await _repository.GetQuery()
            .Where(x => !x.IsDeleted)
            .OrderBy(x => x.CreatedAtUtc)
            .FirstOrDefaultAsync(cancellationToken);

        return new GetCurrentCommissionBaseResult
        {
            CurrentCommissionBase = firstPriceList?.CommissionBase ?? 0
        };
    }
}
