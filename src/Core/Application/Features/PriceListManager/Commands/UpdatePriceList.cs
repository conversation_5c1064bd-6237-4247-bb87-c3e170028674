﻿using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;

namespace Application.Features.PriceListManager.Commands;

public class UpdatePriceListResult
{
    public PriceList? Data { get; set; }
}

public class UpdatePriceListRequest : IRequest<UpdatePriceListResult>
{
    public string? Id { get; init; }
    public string? PriceListCode { get; init; }
    public string? ListPrice { get; init; }
    public decimal? Points { get; init; }
    public int? Term { get; init; }
    public string? Type { get; init; }
    public string? Description { get; init; }
    public DateTime? DateEffectiveTo { get; init; }
    public DateTime? DateEffectiveFrom { get; init; }
    public decimal? FirstDownPayment { get; init; }
    public decimal? SecondDownPayment { get; init; }
    public decimal? InstallmentValue { get; init; }
    public decimal? TotalInstValWithDownPayment { get; init; }
    public decimal? TotalSales { get; init; }
    public decimal? CommissionBase { get; init; }
    public string? UpdatedById { get; init; }
    public bool IsActive { get; init; } = true;
}

public class UpdatePriceListValidator : AbstractValidator<UpdatePriceListRequest>
{
    public UpdatePriceListValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
        RuleFor(x => x.PriceListCode).NotEmpty();
        RuleFor(x => x.Description).NotEmpty();
        RuleFor(x => x.ListPrice).NotEmpty();
        RuleFor(x => x.Points).NotEmpty();
        RuleFor(x => x.Term).NotEmpty();
        RuleFor(x => x.Type).NotEmpty();
        RuleFor(x => x.DateEffectiveTo).NotEmpty();
        RuleFor(x => x.DateEffectiveFrom).NotEmpty();
        RuleFor(x => x.FirstDownPayment).NotEmpty();
        RuleFor(x => x.SecondDownPayment).NotEmpty();
        RuleFor(x => x.InstallmentValue).NotEmpty();
        RuleFor(x => x.TotalInstValWithDownPayment).NotEmpty();
        RuleFor(x => x.TotalSales).NotEmpty();
        RuleFor(x => x.CommissionBase).NotEmpty();
        RuleFor(x => x.IsActive).NotNull();
    }
}

public class UpdatePriceListHandler : IRequestHandler<UpdatePriceListRequest, UpdatePriceListResult>
{
    private readonly ICommandRepository<PriceList> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public UpdatePriceListHandler(
        ICommandRepository<PriceList> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<UpdatePriceListResult> Handle(UpdatePriceListRequest request, CancellationToken cancellationToken)
    {

        var entity = await _repository.GetAsync(request.Id ?? string.Empty, cancellationToken);

        if (entity == null)
        {
            throw new Exception($"Entity not found: {request.Id}");
        }

        entity.UpdatedById = request.UpdatedById;

        entity.PriceListCode = request.PriceListCode;
        entity.Description = request.Description;
        entity.ListPrice = request.ListPrice;
        entity.Points = request.Points;
        entity.Term = request.Term;
        entity.Type = request.Type;
        entity.DateEffectiveTo = request.DateEffectiveTo;
        entity.DateEffectiveFrom = request.DateEffectiveFrom;
        entity.IsActive = request.IsActive;
        entity.FirstDownPayment = request.FirstDownPayment;
        entity.SecondDownPayment = request.SecondDownPayment;
        entity.InstallmentValue = request.InstallmentValue;
        entity.TotalInstValWithDownPayment = request.TotalInstValWithDownPayment;
        entity.TotalSales = request.TotalSales;
        entity.CommissionBase = request.CommissionBase;


        _repository.Update(entity);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new UpdatePriceListResult
        {
            Data = entity
        };
    }
}

