﻿using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.PriceListManager.Commands;

public class CreatePriceListResult
{
    public PriceList? Data { get; set; }
    
}

public class CreatePriceListRequest : IRequest<CreatePriceListResult>
    {
    public string? PriceListCode { get; init; }
    public string? ListPrice { get; init; }
    public decimal? Points { get; init; }
    public int? Term { get; init; }
    public string? Type { get; init; }
    public string? Description { get; init; }
    public DateTime? DateEffectiveTo { get; init; }
    public DateTime? DateEffectiveFrom { get; init; }
    public decimal? FirstDownPayment { get; init; }
    public decimal? SecondDownPayment { get; init; }
    public decimal? InstallmentValue { get; init; }
    public decimal? TotalInstValWithDownPayment { get; init; }
    public decimal? TotalSales { get; init; }
    public decimal? CommissionBase { get; init; }
    public bool IsActive { get; init; } = true;
    public string? CreatedById { get; init; }
}

public class CreatePriceListValidator : AbstractValidator<CreatePriceListRequest>
{
    public CreatePriceListValidator()
    {
        RuleFor(x => x.PriceListCode).NotEmpty();
        RuleFor(x => x.ListPrice).NotEmpty();
        RuleFor(x => x.Points).NotEmpty();
        RuleFor(x => x.Term).NotEmpty();
        RuleFor(x => x.Type).NotEmpty();
        RuleFor(x => x.Description).NotEmpty();
        RuleFor(x => x.DateEffectiveTo).NotEmpty();
        RuleFor(x => x.DateEffectiveFrom).NotEmpty();
        RuleFor(x => x.FirstDownPayment).NotEmpty();
        RuleFor(x => x.SecondDownPayment).NotEmpty();
        RuleFor(x => x.InstallmentValue).NotEmpty();
        RuleFor(x => x.TotalInstValWithDownPayment).NotEmpty();
        RuleFor(x => x.TotalSales).NotEmpty();
        RuleFor(x => x.CommissionBase).NotEmpty();
        RuleFor(x => x.IsActive).NotNull();
    }
}

public class CreatePriceListHandler : IRequestHandler<CreatePriceListRequest, CreatePriceListResult>
{
    private readonly ICommandRepository<PriceList> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public CreatePriceListHandler(
        ICommandRepository<PriceList> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<CreatePriceListResult> Handle(CreatePriceListRequest request, CancellationToken cancellationToken = default)
    {

        // Check for duplicate PriceListCOde
        var existingPriceListCode = await _repository.GetQuery()
            .Where(x => x.PriceListCode == request.PriceListCode && !x.IsDeleted)
            .FirstOrDefaultAsync(cancellationToken);

        if (existingPriceListCode != null)
        {
            throw new Exception($"Price List Code :'{request.PriceListCode}' already exists.");
        }

        

        var entity = new PriceList();
        entity.CreatedById = request.CreatedById;

        entity.PriceListCode = request.PriceListCode;
        entity.ListPrice = request.ListPrice;
        entity.Points = request.Points;
        entity.Term = request.Term;
        entity.Type = request.Type;
        entity.Description = request.Description;
        entity.DateEffectiveTo = request.DateEffectiveTo;
        entity.DateEffectiveFrom = request.DateEffectiveFrom;
        entity.FirstDownPayment = request.FirstDownPayment;
        entity.SecondDownPayment = request.SecondDownPayment;
        entity.InstallmentValue = request.InstallmentValue;
        entity.TotalInstValWithDownPayment = request.TotalInstValWithDownPayment;
        entity.TotalSales = request.TotalSales;
        entity.CommissionBase = request.CommissionBase;
        entity.IsActive = request.IsActive;

        await _repository.CreateAsync(entity, cancellationToken);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new CreatePriceListResult
        {
            Data = entity
        };
    }
}