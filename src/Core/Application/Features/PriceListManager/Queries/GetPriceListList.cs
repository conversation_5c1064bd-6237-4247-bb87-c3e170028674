﻿using Application.Common.CQS.Queries;
using Application.Common.Extensions;
using AutoMapper;
using Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.PriceListManager.Queries;

public record GetPriceListListDto
{
    public string? Id { get; init; }
    public string? PriceListCode { get; init; }
    public string? Description { get; init; }
    public decimal? Points { get; init; }
    public int? Term { get; init; }
    public string? Type { get; init; }
    public string? ListPrice { get; init; }
    public DateTime? DateEffectiveFrom { get; init; }
    public DateTime? DateEffectiveTo { get; init; }
    public decimal? FirstDownPayment { get; init; }
    public decimal? SecondDownPayment { get; init; }
    public decimal? InstallmentValue { get; init; }
    public decimal? TotalInstValWithDownPayment { get; init; }
    public decimal? TotalSales { get; init; }
    public decimal? CommissionBase { get; init; }
    public bool IsActive { get; init; }
    public DateTime? CreatedAtUtc { get; init; }
}

public class GetPriceListListProfile : Profile
{
    public GetPriceListListProfile()
    {
        CreateMap<PriceList, GetPriceListListDto>();
    }
}

public class GetPriceListListResult
{
    public List<GetPriceListListDto>? Data { get; init; }
}

public class GetPriceListListRequest : IRequest<GetPriceListListResult>
{
    public bool IsDeleted { get; init; } = false;
}


public class GetPriceListListHandler : IRequestHandler<GetPriceListListRequest, GetPriceListListResult>
{
    private readonly IMapper _mapper;
    private readonly IQueryContext _context;

    public GetPriceListListHandler(IMapper mapper, IQueryContext context)
    {
        _mapper = mapper;
        _context = context;
    }

    public async Task<GetPriceListListResult> Handle(GetPriceListListRequest request, CancellationToken cancellationToken)
    {
        var query = _context
            .PriceList
            .AsNoTracking()
            .IsDeletedEqualTo(request.IsDeleted)
            .AsQueryable();

        var entities = await query.ToListAsync(cancellationToken);

        var dtos = _mapper.Map<List<GetPriceListListDto>>(entities);

        return new GetPriceListListResult
        {
            Data = dtos
        };
    }


}



