﻿using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;

namespace Application.Features.BankManager.Commands;

public class UpdateBankResult
{
    public Bank? Data { get; set; }
}

public class UpdateBankRequest : IRequest<UpdateBankResult>
{
    public string? Id { get; init; }
    public string? BankName { get; init; }
    public string? SwiftAddress { get; init; }
    public string? SoftCode { get; init; }
    public string? UpdatedById { get; init; }
}

public class UpdateBankValidator : AbstractValidator<UpdateBankRequest>
{
    public UpdateBankValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
        RuleFor(x => x.BankName).NotEmpty();
        RuleFor(x => x.SwiftAddress).NotEmpty();
        RuleFor(x => x.SoftCode).NotEmpty();
    }
}

public class UpdateBankHandler : IRequestHandler<UpdateBankRequest, UpdateBankResult>
{
    private readonly ICommandRepository<Bank> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public UpdateBankHandler(
        ICommandRepository<Bank> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<UpdateBankResult> Handle(UpdateBankRequest request, CancellationToken cancellationToken)
    {

        var entity = await _repository.GetAsync(request.Id ?? string.Empty, cancellationToken);

        if (entity == null)
        {
            throw new Exception($"Entity not found: {request.Id}");
        }

        entity.UpdatedById = request.UpdatedById;

        entity.BankName = request.BankName;
        entity.SwiftAddress = request.SwiftAddress;
        entity.SoftCode = request.SoftCode;

        _repository.Update(entity);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new UpdateBankResult
        {
            Data = entity
        };
    }
}

