﻿using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;

namespace Application.Features.BankManager.Commands;

public class DeleteBankResult
{
    public Bank? Data { get; set; }
}

public class DeleteBankRequest : IRequest<DeleteBankResult>
{
    public string? Id { get; init; }
    public string? DeletedById { get; init; }
}

public class DeleteBankValidator : AbstractValidator<DeleteBankRequest>
{
    public DeleteBankValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
    }
}

public class DeleteBankHandler : IRequestHandler<DeleteBankRequest, DeleteBankResult>
{
    private readonly ICommandRepository<Bank> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public DeleteBankHandler(
        ICommandRepository<Bank> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<DeleteBankResult> Handle(DeleteBankRequest request, CancellationToken cancellationToken)
    {

        var entity = await _repository.GetAsync(request.Id ?? string.Empty, cancellationToken);

        if (entity == null)
        {
            throw new Exception($"Entity not found: {request.Id}");
        }

        entity.UpdatedById = request.DeletedById;

        _repository.Delete(entity);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new DeleteBankResult
        {
            Data = entity
        };
    }
}

