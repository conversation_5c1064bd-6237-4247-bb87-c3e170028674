﻿using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.BankManager.Commands;

public class CreateBankResult
{
    public Bank? Data { get; set; }
    
}

public class CreateBankRequest : IRequest<CreateBankResult>
{
    public string? BankName { get; init; }
    public string? SwiftAddress { get; init; }
    public string? SoftCode { get; init; }
    public string? CreatedById { get; init; }
}

public class CreateBankValidator : AbstractValidator<CreateBankRequest>
{
    public CreateBankValidator()
    {
        RuleFor(x => x.BankName).NotEmpty();
        RuleFor(x => x.<PERSON>ddress).NotEmpty();
        RuleFor(x => x.SoftCode).NotEmpty();
    }
}

public class CreateBankHandler : IRequestHandler<CreateBankRequest, CreateBankResult>
{
    private readonly ICommandRepository<Bank> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public CreateBankHandler(
        ICommandRepository<Bank> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<CreateBankResult> Handle(CreateBankRequest request, CancellationToken cancellationToken = default)
    {

        // Check for duplicate DivisionCode
        var existingBank = await _repository.GetQuery()
            .Where(x => x.BankName == request.BankName && !x.IsDeleted)
            .FirstOrDefaultAsync(cancellationToken);

        if (existingBank != null)
        {
            throw new Exception($"Bank Name :'{request.BankName}' already exists.");
        }

        var existingAddress = await _repository.GetQuery()
            .Where(x => x.SwiftAddress == request.SwiftAddress && !x.IsDeleted)
            .FirstOrDefaultAsync(cancellationToken);

        if (existingAddress != null)
        {
            throw new Exception($"Swift Address :'{request.SwiftAddress}' already exists.");
        }

        var existingCode = await _repository.GetQuery()
            .Where(x => x.SoftCode == request.SoftCode && !x.IsDeleted)
            .FirstOrDefaultAsync(cancellationToken);

        if (existingCode != null)
        {
            throw new Exception($"Soft Code :'{request.SoftCode}' already exists.");
        }

        var entity = new Bank();
        entity.CreatedById = request.CreatedById;

        entity.BankName = request.BankName;
        entity.SwiftAddress = request.SwiftAddress;
        entity.SoftCode = request.SoftCode;

        await _repository.CreateAsync(entity, cancellationToken);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new CreateBankResult
        {
            Data = entity
        };
    }
}