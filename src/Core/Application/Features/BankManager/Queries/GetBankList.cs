﻿using Application.Common.CQS.Queries;
using Application.Common.Extensions;
using AutoMapper;
using Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.BankManager.Queries;

public record GetBankListDto
{
    public string? Id { get; init; }
    public string? BankName { get; init; }
    public string? SwiftAddress { get; init; }
    public string? SoftCode { get; init; }
    public DateTime? CreatedAtUtc { get; init; }
}

public class GetBankListProfile : Profile
{
    public GetBankListProfile()
    {
        CreateMap<Bank, GetBankListDto>();
    }
}

public class GetBankListResult
{
    public List<GetBankListDto>? Data { get; init; }
}

public class GetBankListRequest : IRequest<GetBankListResult>
{
    public bool IsDeleted { get; init; } = false;
}


public class GetBankListHandler : IRequestHandler<GetBankListRequest, GetBankListResult>
{
    private readonly IMapper _mapper;
    private readonly IQueryContext _context;

    public GetBankListHandler(IMapper mapper, IQueryContext context)
    {
        _mapper = mapper;
        _context = context;
    }

    public async Task<GetBankListResult> Handle(GetBankListRequest request, CancellationToken cancellationToken)
    {
        var query = _context
            .Bank
            .AsNoTracking()
            .IsDeletedEqualTo(request.IsDeleted)
            .AsQueryable();

        var entities = await query.ToListAsync(cancellationToken);

        var dtos = _mapper.Map<List<GetBankListDto>>(entities);

        return new GetBankListResult
        {
            Data = dtos
        };
    }


}



