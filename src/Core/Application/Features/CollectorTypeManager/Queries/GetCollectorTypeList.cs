﻿using Application.Common.CQS.Queries;
using Application.Common.Extensions;
using AutoMapper;
using Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.CollectorTypeManager.Queries;

public record GetCollectorTypeListDto
{
    public string? Id { get; init; }
    public string? CollectorTypeCode { get; init; }
    public string? Description { get; init; }
    public DateTime? CreatedAtUtc { get; init; }
}

public class GetCollectorTypeListProfile : Profile
{
    public GetCollectorTypeListProfile()
    {
        CreateMap<CollectorType, GetCollectorTypeListDto>();
    }
}

public class GetCollectorTypeListResult
{
    public List<GetCollectorTypeListDto>? Data { get; init; }
}

public class GetCollectorTypeListRequest : IRequest<GetCollectorTypeListResult>
{
    public bool IsDeleted { get; init; } = false;
}


public class GetCollectorTypeListHandler : IRequestHandler<GetCollectorTypeListRequest, GetCollectorTypeListResult>
{
    private readonly IMapper _mapper;
    private readonly IQueryContext _context;

    public GetCollectorTypeListHandler(IMapper mapper, IQueryContext context)
    {
        _mapper = mapper;
        _context = context;
    }

    public async Task<GetCollectorTypeListResult> Handle(GetCollectorTypeListRequest request, CancellationToken cancellationToken)
    {
        var query = _context
            .CollectorType
            .AsNoTracking()
            .IsDeletedEqualTo(request.IsDeleted)
            .AsQueryable();

        var entities = await query.ToListAsync(cancellationToken);

        var dtos = _mapper.Map<List<GetCollectorTypeListDto>>(entities);

        return new GetCollectorTypeListResult
        {
            Data = dtos
        };
    }


}



