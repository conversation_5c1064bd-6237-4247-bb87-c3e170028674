﻿using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;


namespace Application.Features.CollectorTypeManager.Commands;

public class CreateCollectorTypeResult
{
    public CollectorType? Data { get; set; }
}

public class CreateCollectorTypeRequest : IRequest<CreateCollectorTypeResult>
{
    public string? CollectorTypeCode { get; init; }
    public string? Description { get; init; }
    public string? CreatedById { get; init; }
}

public class CreateCollectorTypeValidator : AbstractValidator<CreateCollectorTypeRequest>
{
    public CreateCollectorTypeValidator()
    {
        RuleFor(x => x.CollectorTypeCode).NotEmpty();
        RuleFor(x => x.Description).NotEmpty();
    }
}

public class CreateCollectorTypeHandler : IRequestHandler<CreateCollectorTypeRequest, CreateCollectorTypeResult>
{
    private readonly ICommandRepository<CollectorType> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public CreateCollectorTypeHandler(
        ICommandRepository<CollectorType> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<CreateCollectorTypeResult> Handle(CreateCollectorTypeRequest request, CancellationToken cancellationToken = default)
    {
        var existingDivision = await _repository.GetQuery()
            .Where(x => x.CollectorTypeCode == request.CollectorTypeCode && !x.IsDeleted)
            .FirstOrDefaultAsync(cancellationToken);

        if (existingDivision != null)
        {
            throw new Exception($"CollectorType Code :'{request.CollectorTypeCode}' already exists.");
        }

        var entity = new CollectorType();
        entity.CreatedById = request.CreatedById;

        entity.CollectorTypeCode = request.CollectorTypeCode;
        entity.Description = request.Description;

        await _repository.CreateAsync(entity, cancellationToken);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new CreateCollectorTypeResult
        {
            Data = entity
        };
    }
}