﻿using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;

namespace Application.Features.CollectorTypeManager.Commands;

public class UpdateCollectorTypeResult
{
    public CollectorType? Data { get; set; }
}

public class UpdateCollectorTypeRequest : IRequest<UpdateCollectorTypeResult>
{
    public string? Id { get; init; }
    public string? CollectorTypeCode { get; init; }
    public string? Description { get; init; }
    public string? UpdatedById { get; init; }
}

public class UpdateCollectorTypeValidator : AbstractValidator<UpdateCollectorTypeRequest>
{
    public UpdateCollectorTypeValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
        RuleFor(x => x.CollectorTypeCode).NotEmpty();
        RuleFor(x => x.Description).NotEmpty();

    }
}

public class UpdateCollectorTypeHandler : IRequestHandler<UpdateCollectorTypeRequest, UpdateCollectorTypeResult>
{
    private readonly ICommandRepository<CollectorType> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public UpdateCollectorTypeHandler(
        ICommandRepository<CollectorType> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<UpdateCollectorTypeResult> Handle(UpdateCollectorTypeRequest request, CancellationToken cancellationToken)
    {

        var entity = await _repository.GetAsync(request.Id ?? string.Empty, cancellationToken);

        if (entity == null)
        {
            throw new Exception($"Entity not found: {request.Id}");
        }

        entity.UpdatedById = request.UpdatedById;

        entity.CollectorTypeCode = request.CollectorTypeCode;
        entity.Description = request.Description;

        _repository.Update(entity);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new UpdateCollectorTypeResult
        {
            Data = entity
        };
    }
}

