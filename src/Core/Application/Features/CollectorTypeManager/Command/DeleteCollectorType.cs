﻿using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;

namespace Application.Features.CollectorTypeManager.Commands;

public class DeleteCollectorTypeResult
{
    public CollectorType? Data { get; set; }
}

public class DeleteCollectorTypeRequest : IRequest<DeleteCollectorTypeResult>
{
    public string? Id { get; init; }
    public string? DeletedById { get; init; }
}

public class DeleteCollectorTypeValidator : AbstractValidator<DeleteCollectorTypeRequest>
{
    public DeleteCollectorTypeValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
    }
}

public class DeleteCollectorTypeHandler : IRequestHandler<DeleteCollectorTypeRequest, DeleteCollectorTypeResult>
{
    private readonly ICommandRepository<CollectorType> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public DeleteCollectorTypeHandler(
        ICommandRepository<CollectorType> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<DeleteCollectorTypeResult> Handle(DeleteCollectorTypeRequest request, CancellationToken cancellationToken)
    {

        var entity = await _repository.GetAsync(request.Id ?? string.Empty, cancellationToken);

        if (entity == null)
        {
            throw new Exception($"Entity not found: {request.Id}");
        }

        entity.UpdatedById = request.DeletedById;

        _repository.Delete(entity);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new DeleteCollectorTypeResult
        {
            Data = entity
        };
    }
}

