﻿using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;

namespace Application.Features.ProductCodeManager.Commands;

public class DeleteProductCodeResult
{
    public ProductCode? Data { get; set; }
}

public class DeleteProductCodeRequest : IRequest<DeleteProductCodeResult>
{
    public string? Id { get; init; }
    public string? DeletedById { get; init; }
}

public class DeleteProductCodeValidator : AbstractValidator<DeleteProductCodeRequest>
{
    public DeleteProductCodeValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
    }
}

public class DeleteProductCodeHandler : IRequestHandler<DeleteProductCodeRequest, DeleteProductCodeResult>
{
    private readonly ICommandRepository<ProductCode> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public DeleteProductCodeHandler(
        ICommandRepository<ProductCode> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<DeleteProductCodeResult> Handle(DeleteProductCodeRequest request, CancellationToken cancellationToken)
    {

        var entity = await _repository.GetAsync(request.Id ?? string.Empty, cancellationToken);

        if (entity == null)
        {
            throw new Exception($"Entity not found: {request.Id}");
        }

        entity.UpdatedById = request.DeletedById;

        _repository.Delete(entity);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new DeleteProductCodeResult
        {
            Data = entity
        };
    }
}

