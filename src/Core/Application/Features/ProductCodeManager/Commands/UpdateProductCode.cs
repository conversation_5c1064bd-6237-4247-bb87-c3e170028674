﻿using Application.Common.Repositories;
using Microsoft.EntityFrameworkCore;
using Domain.Entities;
using FluentValidation;
using MediatR;

namespace Application.Features.ProductCodeManager.Commands;

public class UpdateProductCodeResult
{
    public ProductCode? Data { get; set; }
}

public class UpdateProductCodeRequest : IRequest<UpdateProductCodeResult>
{
    public string? Id { get; init; }
    public string? Code { get; init; }
    public string? SerialNumber { get; init; }
    public string? Description { get; init; }
    public string? Source { get; init; }
    public decimal? Points { get; init; }
    public string? UpdatedById { get; init; }
    public bool IsActive { get; init; } = true;
}

public class UpdateProductCodeValidator : AbstractValidator<UpdateProductCodeRequest>
{
    public UpdateProductCodeValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
        RuleFor(x => x.Code).NotEmpty();
        RuleFor(x => x.SerialNumber).NotEmpty();
        RuleFor(x => x.Description).NotEmpty();
        RuleFor(x => x.Source).NotEmpty();
        RuleFor(x => x.Points).NotEmpty();
        RuleFor(x => x.IsActive).NotNull();
    }
}

public class UpdateProductCodeHandler : IRequestHandler<UpdateProductCodeRequest, UpdateProductCodeResult>
{
    private readonly ICommandRepository<ProductCode> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public UpdateProductCodeHandler(
        ICommandRepository<ProductCode> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<UpdateProductCodeResult> Handle(UpdateProductCodeRequest request, CancellationToken cancellationToken)
    {

        var entity = await _repository.GetAsync(request.Id ?? string.Empty, cancellationToken);

        if (entity == null)
        {
            throw new Exception($"Entity not found: {request.Id}");
        }

        entity.UpdatedById = request.UpdatedById;

        entity.Code = request.Code;
        entity.SerialNumber = request.SerialNumber;
        entity.Description = request.Description;
        entity.Source = request.Source;
        entity.Points = request.Points;
        entity.IsActive = request.IsActive;

        _repository.Update(entity);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new UpdateProductCodeResult
        {
            Data = entity
        };
    }
}

