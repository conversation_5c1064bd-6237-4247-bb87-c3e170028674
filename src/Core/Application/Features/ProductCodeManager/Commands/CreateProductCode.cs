﻿using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.ProductCodeManager.Commands;

public class CreateProductCodeResult
{
    public ProductCode? Data { get; set; }
    
}

public class CreateProductCodeRequest : IRequest<CreateProductCodeResult>
{
    public string? Code { get; init; }
    public string? SerialNumber { get; init; }
    public string? Description { get; init; }
    public string? Source { get; init; }
    public decimal? Points { get; init; }
    public bool IsActive { get; init; } = true;
    public string? CreatedById { get; init; }
}

public class CreateProductCodeValidator : AbstractValidator<CreateProductCodeRequest>
{
    public CreateProductCodeValidator()
    {
        RuleFor(x => x.Code).NotEmpty();
        RuleFor(x => x.SerialNumber).NotEmpty();
        RuleFor(x => x.Description).NotEmpty();
        RuleFor(x => x.Source).NotEmpty();
        RuleFor(x => x.Points).NotEmpty();
        RuleFor(x => x.IsActive).NotNull();
    }
}

public class CreateProductCodeHandler : IRequestHandler<CreateProductCodeRequest, CreateProductCodeResult>
{
    private readonly ICommandRepository<ProductCode> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public CreateProductCodeHandler(
        ICommandRepository<ProductCode> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<CreateProductCodeResult> Handle(CreateProductCodeRequest request, CancellationToken cancellationToken = default)
    {

        // Check for duplicate Product Code
        var existingProductCode = await _repository.GetQuery()
            .Where(x => x.Code == request.Code && !x.IsDeleted)
            .FirstOrDefaultAsync(cancellationToken);

        if (existingProductCode != null)
        {
            throw new Exception($"ProductCode Code :'{request.Code}' already exists.");
        }

        var existingSerialNumber = await _repository.GetQuery()
            .Where(x => x.SerialNumber == request.SerialNumber && !x.IsDeleted)
            .FirstOrDefaultAsync(cancellationToken);

        if (existingSerialNumber != null)
        {
            throw new Exception($"SerialNumber :'{request.SerialNumber}' already exists.");
        }

        var entity = new ProductCode();
        entity.CreatedById = request.CreatedById;

        entity.Code = request.Code;
        entity.SerialNumber = request.SerialNumber;
        entity.Description = request.Description;
        entity.Source = request.Source;
        entity.Points = request.Points;
        entity.IsActive = request.IsActive;

        await _repository.CreateAsync(entity, cancellationToken);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new CreateProductCodeResult
        {
            Data = entity
        };
    }
}