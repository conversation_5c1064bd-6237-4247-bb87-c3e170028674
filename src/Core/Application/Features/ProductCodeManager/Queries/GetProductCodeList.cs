﻿using Application.Common.CQS.Queries;
using Application.Common.Extensions;
using AutoMapper;
using Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.ProductCodeManager.Queries;

public record GetProductCodeListDto
{
    public string? Id { get; init; }
    public string? Code { get; init; }
    public string? SerialNumber { get; init; }
    public string? Description { get; init; }
    public decimal? Points { get; init; }
    public string? Source { get; init; }
    public bool IsActive { get; init; }
    public DateTime? CreatedAtUtc { get; init; }
}

public class GetProductCodeListProfile : Profile
{
    public GetProductCodeListProfile()
    {
        CreateMap<ProductCode, GetProductCodeListDto>();
    }
}

public class GetProductCodeListResult
{
    public List<GetProductCodeListDto>? Data { get; init; }
}

public class GetProductCodeListRequest : IRequest<GetProductCodeListResult>
{
    public bool IsDeleted { get; init; } = false;
}


public class GetProductCodeListHandler : IRequestHandler<GetProductCodeListRequest, GetProductCodeListResult>
{
    private readonly IMapper _mapper;
    private readonly IQueryContext _context;

    public GetProductCodeListHandler(IMapper mapper, IQueryContext context)
    {
        _mapper = mapper;
        _context = context;
    }

    public async Task<GetProductCodeListResult> Handle(GetProductCodeListRequest request, CancellationToken cancellationToken)
    {
        var query = _context
            .ProductCode
            .AsNoTracking()
            .IsDeletedEqualTo(request.IsDeleted)
            .AsQueryable();

        var entities = await query.ToListAsync(cancellationToken);

        var dtos = _mapper.Map<List<GetProductCodeListDto>>(entities);

        return new GetProductCodeListResult
        {
            Data = dtos
        };
    }


}



