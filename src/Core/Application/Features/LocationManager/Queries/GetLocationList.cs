﻿using Application.Common.CQS.Queries;
using Application.Common.Extensions;
using AutoMapper;
using Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.LocationManager.Queries;

public record GetLocationListDto
{
    public string? Id { get; init; }
    public string? LocationCode { get; init; }
    public string? Description { get; init; }
    public DateTime? CreatedAtUtc { get; init; }
}

public class GetLocationListProfile : Profile
{
    public GetLocationListProfile()
    {
        CreateMap<Location, GetLocationListDto>();
    }
}

public class GetLocationListResult
{
    public List<GetLocationListDto>? Data { get; init; }
}

public class GetLocationListRequest : IRequest<GetLocationListResult>
{
    public bool IsDeleted { get; init; } = false;
}


public class GetLocationListHandler : IRequestHandler<GetLocationListRequest, GetLocationListResult>
{
    private readonly IMapper _mapper;
    private readonly IQueryContext _context;

    public GetLocationListHandler(IMapper mapper, IQueryContext context)
    {
        _mapper = mapper;
        _context = context;
    }

    public async Task<GetLocationListResult> Handle(GetLocationListRequest request, CancellationToken cancellationToken)
    {
        var query = _context
            .Location
            .AsNoTracking()
            .IsDeletedEqualTo(request.IsDeleted)
            .AsQueryable();

        var entities = await query.ToListAsync(cancellationToken);

        var dtos = _mapper.Map<List<GetLocationListDto>>(entities);

        return new GetLocationListResult
        {
            Data = dtos
        };
    }


}



