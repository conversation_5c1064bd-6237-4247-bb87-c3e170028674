﻿using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;

namespace Application.Features.LocationManager.Commands;

public class DeleteLocationResult
{
    public Location? Data { get; set; }
}

public class DeleteLocationRequest : IRequest<DeleteLocationResult>
{
    public string? Id { get; init; }
    public string? DeletedById { get; init; }
}

public class DeleteLocationValidator : AbstractValidator<DeleteLocationRequest>
{
    public DeleteLocationValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
    }
}

public class DeleteLocationHandler : IRequestHandler<DeleteLocationRequest, DeleteLocationResult>
{
    private readonly ICommandRepository<Location> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public DeleteLocationHandler(
        ICommandRepository<Location> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<DeleteLocationResult> Handle(DeleteLocationRequest request, CancellationToken cancellationToken)
    {

        var entity = await _repository.GetAsync(request.Id ?? string.Empty, cancellationToken);

        if (entity == null)
        {
            throw new Exception($"Entity not found: {request.Id}");
        }

        entity.UpdatedById = request.DeletedById;

        _repository.Delete(entity);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new DeleteLocationResult
        {
            Data = entity
        };
    }
}

