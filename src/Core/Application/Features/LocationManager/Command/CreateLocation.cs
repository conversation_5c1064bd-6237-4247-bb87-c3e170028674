﻿using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;


namespace Application.Features.LocationManager.Commands;

public class CreateLocationResult
{
    public Location? Data { get; set; }
}

public class CreateLocationRequest : IRequest<CreateLocationResult>
{
    public string? LocationCode { get; init; }
    public string? Description { get; init; }
    public string? CreatedById { get; init; }
}

public class CreateLocationValidator : AbstractValidator<CreateLocationRequest>
{
    public CreateLocationValidator()
    {
        RuleFor(x => x.LocationCode).NotEmpty();
        RuleFor(x => x.Description).NotEmpty();
    }
}

public class CreateLocationHandler : IRequestHandler<CreateLocationRequest, CreateLocationResult>
{
    private readonly ICommandRepository<Location> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public CreateLocationHandler(
        ICommandRepository<Location> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<CreateLocationResult> Handle(CreateLocationRequest request, CancellationToken cancellationToken = default)
    {
        var existingDivision = await _repository.GetQuery()
            .Where(x => x.LocationCode == request.LocationCode && !x.IsDeleted)
            .FirstOrDefaultAsync(cancellationToken);

        if (existingDivision != null)
        {
            throw new Exception($"Location Code :'{request.LocationCode}' already exists.");
        }

        var entity = new Location();
        entity.CreatedById = request.CreatedById;

        entity.LocationCode = request.LocationCode;
        entity.Description = request.Description;

        await _repository.CreateAsync(entity, cancellationToken);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new CreateLocationResult
        {
            Data = entity
        };
    }
}