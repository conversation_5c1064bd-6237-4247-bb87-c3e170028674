﻿using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;

namespace Application.Features.LocationManager.Commands;

public class UpdateLocationResult
{
    public Location? Data { get; set; }
}

public class UpdateLocationRequest : IRequest<UpdateLocationResult>
{
    public string? Id { get; init; }
    public string? LocationCode { get; init; }
    public string? Description { get; init; }
    public string? UpdatedById { get; init; }
}

public class UpdateLocationValidator : AbstractValidator<UpdateLocationRequest>
{
    public UpdateLocationValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
        RuleFor(x => x.LocationCode).NotEmpty();
        RuleFor(x => x.Description).NotEmpty();

    }
}

public class UpdateLocationHandler : IRequestHandler<UpdateLocationRequest, UpdateLocationResult>
{
    private readonly ICommandRepository<Location> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public UpdateLocationHandler(
        ICommandRepository<Location> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<UpdateLocationResult> Handle(UpdateLocationRequest request, CancellationToken cancellationToken)
    {

        var entity = await _repository.GetAsync(request.Id ?? string.Empty, cancellationToken);

        if (entity == null)
        {
            throw new Exception($"Entity not found: {request.Id}");
        }

        entity.UpdatedById = request.UpdatedById;

        entity.LocationCode = request.LocationCode;
        entity.Description = request.Description;

        _repository.Update(entity);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new UpdateLocationResult
        {
            Data = entity
        };
    }
}

