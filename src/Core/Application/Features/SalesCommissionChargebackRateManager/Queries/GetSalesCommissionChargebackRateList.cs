﻿using Application.Common.CQS.Queries;
using Application.Common.Extensions;
using AutoMapper;
using Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.SalesCommissionChargebackRateManager.Queries;

public record GetSalesCommissionChargebackRateListDto
{
    public string? Id { get; init; }
    public string? CancellationPeriod { get; init; }
    public string? CollectionRange { get; init; }
    public decimal? CollectionRate { get; init; }
    public decimal? ChargebackRate { get; init; }
    public string? ChargebackInterval { get; init; }
    public string? SalesCommissionChargebackRateCode { get; init; }
    public bool IsActive { get; init; } = true;
    public DateTime? CreatedAtUtc { get; init; }
}

public class GetSalesCommissionChargebackRateListProfile : Profile
{
    public GetSalesCommissionChargebackRateListProfile()
    {
        CreateMap<SalesCommissionChargebackRate, GetSalesCommissionChargebackRateListDto>();
    }
}

public class GetSalesCommissionChargebackRateListResult
{
    public List<GetSalesCommissionChargebackRateListDto>? Data { get; init; }
}

public class GetSalesCommissionChargebackRateListRequest : IRequest<GetSalesCommissionChargebackRateListResult>
{
    public bool IsDeleted { get; init; } = false;
}


public class GetSalesCommissionChargebackRateListHandler : IRequestHandler<GetSalesCommissionChargebackRateListRequest, GetSalesCommissionChargebackRateListResult>
{
    private readonly IMapper _mapper;
    private readonly IQueryContext _context;

    public GetSalesCommissionChargebackRateListHandler(IMapper mapper, IQueryContext context)
    {
        _mapper = mapper;
        _context = context;
    }

    public async Task<GetSalesCommissionChargebackRateListResult> Handle(GetSalesCommissionChargebackRateListRequest request, CancellationToken cancellationToken)
    {
        var query = _context
            .SalesCommissionChargebackRate
            .AsNoTracking()
            .IsDeletedEqualTo(request.IsDeleted)
            .AsQueryable();

        var entities = await query.ToListAsync(cancellationToken);

        var dtos = _mapper.Map<List<GetSalesCommissionChargebackRateListDto>>(entities);

        return new GetSalesCommissionChargebackRateListResult
        {
            Data = dtos
        };
    }


}



