﻿using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;

namespace Application.Features.SalesCommissionChargebackRateManager.Commands;

public class UpdateSalesCommissionChargebackRateResult
{
    public SalesCommissionChargebackRate? Data { get; set; }
}

public class UpdateSalesCommissionChargebackRateRequest : IRequest<UpdateSalesCommissionChargebackRateResult>
{
    public string? Id { get; init; }
    public string? CancellationPeriod { get; init; }
    public string? CollectionRange { get; init; }
    public decimal? CollectionRate { get; init; }
    public decimal? ChargebackRate { get; init; }
    public string? ChargebackInterval { get; init; }
    public string? SalesCommissionChargebackRateCode { get; init; }
    public string? UpdatedById { get; init; }
    public bool IsActive { get; init; } = true;
}

public class UpdateSalesCommissionChargebackRateValidator : AbstractValidator<UpdateSalesCommissionChargebackRateRequest>
{
    public UpdateSalesCommissionChargebackRateValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
        RuleFor(x => x.CancellationPeriod).NotEmpty();
        RuleFor(x => x.CollectionRange).NotEmpty();
        RuleFor(x => x.CollectionRate).NotEmpty();
        RuleFor(x => x.ChargebackRate).NotEmpty();
        RuleFor(x => x.ChargebackInterval).NotEmpty();
        RuleFor(x => x.SalesCommissionChargebackRateCode).NotEmpty();
        RuleFor(x => x.IsActive).NotNull();
    }
}

public class UpdateSalesCommissionChargebackRateHandler : IRequestHandler<UpdateSalesCommissionChargebackRateRequest, UpdateSalesCommissionChargebackRateResult>
{
    private readonly ICommandRepository<SalesCommissionChargebackRate> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public UpdateSalesCommissionChargebackRateHandler(
        ICommandRepository<SalesCommissionChargebackRate> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<UpdateSalesCommissionChargebackRateResult> Handle(UpdateSalesCommissionChargebackRateRequest request, CancellationToken cancellationToken)
    {

        var entity = await _repository.GetAsync(request.Id ?? string.Empty, cancellationToken);

        if (entity == null)
        {
            throw new Exception($"Entity not found: {request.Id}");
        }

        entity.UpdatedById = request.UpdatedById;

        entity.CancellationPeriod = request.CancellationPeriod;
        entity.CollectionRange = request.CollectionRange;
        entity.CollectionRate = request.CollectionRate;
        entity.ChargebackRate = request.ChargebackRate;
        entity.ChargebackInterval = request.ChargebackInterval;
        entity.SalesCommissionChargebackRateCode = request.SalesCommissionChargebackRateCode;
        entity.IsActive = request.IsActive;
        _repository.Update(entity);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new UpdateSalesCommissionChargebackRateResult
        {
            Data = entity
        };
    }
}

