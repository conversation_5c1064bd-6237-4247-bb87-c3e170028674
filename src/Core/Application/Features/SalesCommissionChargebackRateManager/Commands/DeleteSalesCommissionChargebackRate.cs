﻿using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;

namespace Application.Features.SalesCommissionChargebackRateManager.Commands;

public class DeleteSalesCommissionChargebackRateResult
{
    public SalesCommissionChargebackRate? Data { get; set; }
}

public class DeleteSalesCommissionChargebackRateRequest : IRequest<DeleteSalesCommissionChargebackRateResult>
{
    public string? Id { get; init; }
    public string? DeletedById { get; init; }
}

public class DeleteSalesCommissionChargebackRateValidator : AbstractValidator<DeleteSalesCommissionChargebackRateRequest>
{
    public DeleteSalesCommissionChargebackRateValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
    }
}

public class DeleteSalesCommissionChargebackRateHandler : IRequestHandler<DeleteSalesCommissionChargebackRateRequest, DeleteSalesCommissionChargebackRateResult>
{
    private readonly ICommandRepository<SalesCommissionChargebackRate> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public DeleteSalesCommissionChargebackRateHandler(
        ICommandRepository<SalesCommissionChargebackRate> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<DeleteSalesCommissionChargebackRateResult> Handle(DeleteSalesCommissionChargebackRateRequest request, CancellationToken cancellationToken)
    {

        var entity = await _repository.GetAsync(request.Id ?? string.Empty, cancellationToken);

        if (entity == null)
        {
            throw new Exception($"Entity not found: {request.Id}");
        }

        entity.UpdatedById = request.DeletedById;

        _repository.Delete(entity);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new DeleteSalesCommissionChargebackRateResult
        {
            Data = entity
        };
    }
}

