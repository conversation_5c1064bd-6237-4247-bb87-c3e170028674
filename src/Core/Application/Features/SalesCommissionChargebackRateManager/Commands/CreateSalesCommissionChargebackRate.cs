﻿using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.SalesCommissionChargebackRateManager.Commands;

public class CreateSalesCommissionChargebackRateResult
{
    public SalesCommissionChargebackRate? Data { get; set; }
}

public class CreateSalesCommissionChargebackRateRequest : IRequest<CreateSalesCommissionChargebackRateResult>
{
    public string? CancellationPeriod { get; init; }
    public string? CollectionRange { get; init; }
    public decimal? CollectionRate { get; init; }
    public decimal? ChargebackRate { get; init; }
    public string? ChargebackInterval { get; init; }
    public string? SalesCommissionChargebackRateCode { get; init; }
    public bool IsActive { get; init; } = true;
    public string? CreatedById { get; init; }
}

public class CreateSalesCommissionChargebackRateValidator : AbstractValidator<CreateSalesCommissionChargebackRateRequest>
{
    public CreateSalesCommissionChargebackRateValidator()
    {
        RuleFor(x => x.CancellationPeriod).NotEmpty();
        RuleFor(x => x.CollectionRange).NotEmpty();
        RuleFor(x => x.CollectionRate).NotEmpty();
        RuleFor(x => x.ChargebackRate).NotEmpty();
        RuleFor(x => x.ChargebackInterval).NotEmpty();
        RuleFor(x => x.SalesCommissionChargebackRateCode).NotEmpty();
        RuleFor(x => x.IsActive).NotNull();
    }
}

public class CreateSalesCommissionChargebackRateHandler : IRequestHandler<CreateSalesCommissionChargebackRateRequest, CreateSalesCommissionChargebackRateResult>
{
    private readonly ICommandRepository<SalesCommissionChargebackRate> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public CreateSalesCommissionChargebackRateHandler(
        ICommandRepository<SalesCommissionChargebackRate> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<CreateSalesCommissionChargebackRateResult> Handle(CreateSalesCommissionChargebackRateRequest request, CancellationToken cancellationToken = default)
    {
        var existingSalesCommissionChargebackRate = await _repository.GetQuery()
            .Where(x => x.SalesCommissionChargebackRateCode == request.SalesCommissionChargebackRateCode && !x.IsDeleted)
            .FirstOrDefaultAsync(cancellationToken);

        if (existingSalesCommissionChargebackRate != null)
        {
            throw new Exception($"Sales Commission Chargeback Rate Code :'{request.SalesCommissionChargebackRateCode}' already exists.");
        }
        
        var entity = new SalesCommissionChargebackRate();
        entity.CreatedById = request.CreatedById;

        entity.CancellationPeriod = request.CancellationPeriod;
        entity.CollectionRange = request.CollectionRange;
        entity.CollectionRate = request.CollectionRate;
        entity.ChargebackRate = request.ChargebackRate;
        entity.ChargebackInterval = request.ChargebackInterval;
        entity.SalesCommissionChargebackRateCode = request.SalesCommissionChargebackRateCode;
        entity.IsActive = request.IsActive;
        await _repository.CreateAsync(entity, cancellationToken);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new CreateSalesCommissionChargebackRateResult
        {
            Data = entity
        };
    }
}