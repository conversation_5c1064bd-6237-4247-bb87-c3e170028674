using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;

namespace Application.Features.SalesmanRallyBonusManager.Commands;

public class UpdateSalesmanRallyBonusResult
{
    public SalesmanRallyBonus? Data { get; set; }
}

public class UpdateSalesmanRallyBonusRequest : IRequest<UpdateSalesmanRallyBonusResult>
{
    public string? Id { get; init; }
    public string? RegionId { get; init; }
    public string? Description { get; init; }
    public decimal? BonusRate { get; init; }
    public string? CalculationMethod { get; init; }
    public bool IsActive { get; set; } = true;
    public string? UpdatedById { get; init; }
}

public class UpdateSalesmanRallyBonusValidator : AbstractValidator<UpdateSalesmanRallyBonusRequest>
{
    public UpdateSalesmanRallyBonusValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
        RuleFor(x => x.RegionId).NotEmpty();
        RuleFor(x => x.Description).NotEmpty();
        RuleFor(x => x.BonusRate).NotNull().GreaterThanOrEqualTo(0);
        RuleFor(x => x.CalculationMethod).NotEmpty();
        RuleFor(x => x.IsActive).NotNull();
    }
}

public class UpdateSalesmanRallyBonusHandler : IRequestHandler<UpdateSalesmanRallyBonusRequest, UpdateSalesmanRallyBonusResult>
{
    private readonly ICommandRepository<SalesmanRallyBonus> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public UpdateSalesmanRallyBonusHandler(
        ICommandRepository<SalesmanRallyBonus> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<UpdateSalesmanRallyBonusResult> Handle(UpdateSalesmanRallyBonusRequest request, CancellationToken cancellationToken)
    {
        var entity = await _repository.GetAsync(request.Id ?? string.Empty, cancellationToken);

        if (entity == null)
        {
            throw new Exception($"Entity not found: {request.Id}");
        }

        entity.UpdatedById = request.UpdatedById;

        entity.RegionId = request.RegionId;
        entity.Description = request.Description;
        entity.BonusRate = request.BonusRate;
        entity.CalculationMethod = request.CalculationMethod;
        entity.IsActive = request.IsActive;
        
        _repository.Update(entity);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new UpdateSalesmanRallyBonusResult
        {
            Data = entity
        };
    }
} 