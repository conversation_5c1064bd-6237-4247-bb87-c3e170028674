using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;

namespace Application.Features.SalesmanRallyBonusManager.Commands;

public class DeleteSalesmanRallyBonusResult
{
    public SalesmanRallyBonus? Data { get; set; }
}

public class DeleteSalesmanRallyBonusRequest : IRequest<DeleteSalesmanRallyBonusResult>
{
    public string? Id { get; init; }
    public string? DeletedById { get; init; }
}

public class DeleteSalesmanRallyBonusValidator : AbstractValidator<DeleteSalesmanRallyBonusRequest>
{
    public DeleteSalesmanRallyBonusValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
    }
}

public class DeleteSalesmanRallyBonusHandler : IRequestHandler<DeleteSalesmanRallyBonusRequest, DeleteSalesmanRallyBonusResult>
{
    private readonly ICommandRepository<SalesmanRallyBonus> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public DeleteSalesmanRallyBonusHandler(
        ICommandRepository<SalesmanRallyBonus> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<DeleteSalesmanRallyBonusResult> Handle(DeleteSalesmanRallyBonusRequest request, CancellationToken cancellationToken)
    {
        var entity = await _repository.GetAsync(request.Id ?? string.Empty, cancellationToken);

        if (entity == null)
        {
            throw new Exception($"Entity not found: {request.Id}");
        }

        entity.UpdatedById = request.DeletedById;

        _repository.Delete(entity);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new DeleteSalesmanRallyBonusResult
        {
            Data = entity
        };
    }
} 