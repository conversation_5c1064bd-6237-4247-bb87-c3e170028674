using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.SalesmanRallyBonusManager.Commands;

public class CreateSalesmanRallyBonusResult
{
    public SalesmanRallyBonus? Data { get; set; }
}

public class CreateSalesmanRallyBonusRequest : IRequest<CreateSalesmanRallyBonusResult>
{
    public string? RegionId { get; init; }
    public string? Description { get; init; }
    public decimal? BonusRate { get; init; }
    public string? CalculationMethod { get; init; }
    public bool IsActive { get; set; } = true;
    public string? CreatedById { get; init; }
}

public class CreateSalesmanRallyBonusValidator : AbstractValidator<CreateSalesmanRallyBonusRequest>
{
    public CreateSalesmanRallyBonusValidator()
    {
        RuleFor(x => x.RegionId).NotEmpty();
        RuleFor(x => x.Description).NotEmpty();
        RuleFor(x => x.BonusRate).NotNull().GreaterThanOrEqualTo(0);
        RuleFor(x => x.CalculationMethod).NotEmpty();
        RuleFor(x => x.IsActive).NotNull();
    }
}

public class CreateSalesmanRallyBonusHandler : IRequestHandler<CreateSalesmanRallyBonusRequest, CreateSalesmanRallyBonusResult>
{
    private readonly ICommandRepository<SalesmanRallyBonus> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public CreateSalesmanRallyBonusHandler(
        ICommandRepository<SalesmanRallyBonus> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<CreateSalesmanRallyBonusResult> Handle(CreateSalesmanRallyBonusRequest request, CancellationToken cancellationToken = default)
    {
        // Check for duplicate Name
        var existingRallyBonus = await _repository.GetQuery()
            .Where(x => x.RegionId == request.RegionId && !x.IsDeleted)
            .FirstOrDefaultAsync(cancellationToken);

        if (existingRallyBonus != null)
        {
            throw new Exception($"Salesman Rally Bonus Code :'{request.RegionId}' already exists.");
        }

        var entity = new SalesmanRallyBonus();
        entity.CreatedById = request.CreatedById;

        entity.RegionId = request.RegionId;
        entity.Description = request.Description;
        entity.BonusRate = request.BonusRate;
        entity.CalculationMethod = request.CalculationMethod;
        entity.IsActive = request.IsActive;
        await _repository.CreateAsync(entity, cancellationToken);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new CreateSalesmanRallyBonusResult
        {
            Data = entity
        };
    }
} 