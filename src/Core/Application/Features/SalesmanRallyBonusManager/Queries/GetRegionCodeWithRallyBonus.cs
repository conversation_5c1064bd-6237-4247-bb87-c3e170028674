using Application.Common.CQS.Queries;
using Application.Common.Extensions;
using AutoMapper;
using Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.SalesmanRallyBonusManager.Queries;

public record GetRegionCodeWithRallyBonusDto
{
    public string? Id { get; init; }
    public string? RegionCode { get; init; }
    public string? AreaCode { get; init; }
    public string? Description { get; init; }
}

public class GetRegionCodeWithRallyBonusProfile : Profile
{
    public GetRegionCodeWithRallyBonusProfile()
    {
        CreateMap<Region, GetRegionCodeWithRallyBonusDto>();
    }
}

public class GetRegionCodeWithRallyBonusResult
{
    public List<GetRegionCodeWithRallyBonusDto>? Data { get; init; }
}

public class GetRegionCodeWithRallyBonusRequest : IRequest<GetRegionCodeWithRallyBonusResult>
{
    public bool IsDeleted { get; init; } = false;
}

public class GetRegionCodeWithRallyBonusHandler : IRequestHandler<GetRegionCodeWithRallyBonusRequest, GetRegionCodeWithRallyBonusResult>
{
    private readonly IMapper _mapper;
    private readonly IQueryContext _context;

    public GetRegionCodeWithRallyBonusHandler(IMapper mapper, IQueryContext context)
    {
        _mapper = mapper;
        _context = context;
    }

    public async Task<GetRegionCodeWithRallyBonusResult> Handle(GetRegionCodeWithRallyBonusRequest request, CancellationToken cancellationToken)
    {
        // Get regions that are currently used in SalesmanRallyBonus
        var usedRegionIds = await _context
            .SalesmanRallyBonus
            .AsNoTracking()
            .IsDeletedEqualTo(false)
            .Select(x => x.RegionId)
            .Distinct()
            .ToListAsync(cancellationToken);

        var regionsWithRallyBonus = await _context
            .Region
            .AsNoTracking()
            .IsDeletedEqualTo(request.IsDeleted)
            .Where(r => usedRegionIds.Contains(r.Id))
            .ToListAsync(cancellationToken);

        var dtos = _mapper.Map<List<GetRegionCodeWithRallyBonusDto>>(regionsWithRallyBonus);

        return new GetRegionCodeWithRallyBonusResult
        {
            Data = dtos
        };
    }
} 