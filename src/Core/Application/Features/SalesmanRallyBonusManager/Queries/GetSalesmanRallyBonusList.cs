using Application.Common.CQS.Queries;
using Application.Common.Extensions;
using AutoMapper;
using Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.SalesmanRallyBonusManager.Queries;

public record GetSalesmanRallyBonusListDto
{
    public string? Id { get; init; }
    public string? RegionId { get; init; }
    public string? RegionCode { get; init; }
    public string? Description { get; init; }
    public decimal? BonusRate { get; init; }
    public string? CalculationMethod { get; init; }
    public bool IsActive { get; init; }
    public DateTime? CreatedAtUtc { get; init; }
}

public class GetSalesmanRallyBonusListProfile : Profile
{
    public GetSalesmanRallyBonusListProfile()
    {
        CreateMap<SalesmanRallyBonus, GetSalesmanRallyBonusListDto>()
            .ForMember(dest => dest.RegionCode, opt => opt.MapFrom(src => src.Region.RegionCode));
    }
}

public class GetSalesmanRallyBonusListResult
{
    public List<GetSalesmanRallyBonusListDto>? Data { get; init; }
}

public class GetSalesmanRallyBonusListRequest : IRequest<GetSalesmanRallyBonusListResult>
{
    public bool IsDeleted { get; init; } = false;
}

public class GetSalesmanRallyBonusListHandler : IRequestHandler<GetSalesmanRallyBonusListRequest, GetSalesmanRallyBonusListResult>
{
    private readonly IMapper _mapper;
    private readonly IQueryContext _context;

    public GetSalesmanRallyBonusListHandler(IMapper mapper, IQueryContext context)
    {
        _mapper = mapper;
        _context = context;
    }

    public async Task<GetSalesmanRallyBonusListResult> Handle(GetSalesmanRallyBonusListRequest request, CancellationToken cancellationToken)
    {
        var query = _context
            .SalesmanRallyBonus
            .Include(srb => srb.Region)
            .AsNoTracking()
            .IsDeletedEqualTo(request.IsDeleted)
            .AsQueryable();

        var entities = await query.ToListAsync(cancellationToken);

        var dtos = _mapper.Map<List<GetSalesmanRallyBonusListDto>>(entities);

        return new GetSalesmanRallyBonusListResult
        {
            Data = dtos
        };
    }
} 