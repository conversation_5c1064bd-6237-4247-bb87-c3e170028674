﻿using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;


namespace Application.Features.CollectorCodeManager.Commands;

public class CreateCollectorCodeResult
{
    public CollectorCode? Data { get; set; }
}

public class CreateCollectorCodeRequest : IRequest<CreateCollectorCodeResult>
{
    public string? CollectorCodeAssigned { get; init; }
    public string? OutstationOrLocal { get; init; }
    public string? CalculationIndicator { get; init; }
    public decimal? Rate { get; init; }
    public string? CollectorRefCodeId { get; init; }
    public string? CreatedById { get; init; }
}

public class CreateCollectorCodeValidator : AbstractValidator<CreateCollectorCodeRequest>
{
    public CreateCollectorCodeValidator()
    {
        RuleFor(x => x.CollectorCodeAssigned).NotEmpty();
        RuleFor(x => x.OutstationOrLocal).NotEmpty();
        RuleFor(x => x.CalculationIndicator).NotEmpty();
        RuleFor(x => x.Rate).NotEmpty();
        RuleFor(x => x.CollectorRefCodeId).NotEmpty();
    }
}

public class CreateCollectorCodeHandler : IRequestHandler<CreateCollectorCodeRequest, CreateCollectorCodeResult>
{
    private readonly ICommandRepository<CollectorCode> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public CreateCollectorCodeHandler(
        ICommandRepository<CollectorCode> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<CreateCollectorCodeResult> Handle(CreateCollectorCodeRequest request, CancellationToken cancellationToken = default)
    {
        var existingCollectorCode = await _repository.GetQuery()
            .Where(x => x.CollectorCodeAssigned == request.CollectorCodeAssigned && !x.IsDeleted)
            .FirstOrDefaultAsync(cancellationToken);

        if (existingCollectorCode != null)
        {
            throw new Exception($"Collector Code :'{request.CollectorCodeAssigned}' already exists.");
        }

        var entity = new CollectorCode();
        entity.CreatedById = request.CreatedById;

        entity.CollectorCodeAssigned = request.CollectorCodeAssigned;
        entity.OutstationOrLocal = request.OutstationOrLocal;
        entity.CalculationIndicator = request.CalculationIndicator;
        entity.Rate = request.Rate;
        entity.CollectorRefCodeId = request.CollectorRefCodeId;

        await _repository.CreateAsync(entity, cancellationToken);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new CreateCollectorCodeResult
        {
            Data = entity
        };
    }
}