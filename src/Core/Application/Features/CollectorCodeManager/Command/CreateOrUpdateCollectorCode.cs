using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.CollectorCodeManager.Commands;

public class CreateOrUpdateCollectorCodeResult
{
    public List<CollectorCode> Data { get; set; } = new();
    public int CreatedCount { get; set; }
    public int UpdatedCount { get; set; }
}

public class CreateOrUpdateCollectorCodeItem
{
    public string? Id { get; set; }
    public string? CollectorCodeAssigned { get; set; }
    public string? OutstationOrLocal { get; set; }
    public string? CalculationIndicator { get; set; }
    public decimal? Rate { get; set; }
    public string? CollectorRefCodeId { get; set; }
}

public class CreateOrUpdateCollectorCodeRequest : IRequest<CreateOrUpdateCollectorCodeResult>
{
    public List<CreateOrUpdateCollectorCodeItem> CollectorCodes { get; set; } = new();
    public string? UserId { get; set; }
}

public class CreateOrUpdateCollectorCodeValidator : AbstractValidator<CreateOrUpdateCollectorCodeRequest>
{
    public CreateOrUpdateCollectorCodeValidator()
    {
        RuleFor(x => x.CollectorCodes).NotEmpty().WithMessage("At least one collector code is required.");
        RuleFor(x => x.UserId).NotEmpty().WithMessage("User ID is required.");
        
        RuleForEach(x => x.CollectorCodes).ChildRules(item =>
        {
            item.RuleFor(x => x.CollectorCodeAssigned).NotEmpty().WithMessage("Collector Code is required.");
            item.RuleFor(x => x.OutstationOrLocal).NotEmpty().WithMessage("Outstation/Local is required.");
            item.RuleFor(x => x.CalculationIndicator).NotEmpty().WithMessage("Calculation Indicator is required.");
            item.RuleFor(x => x.Rate).NotEmpty().WithMessage("Rate is required.");
            item.RuleFor(x => x.CollectorRefCodeId).NotEmpty().WithMessage("Collector Ref Code ID is required.");
        });
    }
}

public class CreateOrUpdateCollectorCodeHandler : IRequestHandler<CreateOrUpdateCollectorCodeRequest, CreateOrUpdateCollectorCodeResult>
{
    private readonly ICommandRepository<CollectorCode> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public CreateOrUpdateCollectorCodeHandler(
        ICommandRepository<CollectorCode> repository,
        IUnitOfWork unitOfWork)
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<CreateOrUpdateCollectorCodeResult> Handle(CreateOrUpdateCollectorCodeRequest request, CancellationToken cancellationToken = default)
    {
        var result = new CreateOrUpdateCollectorCodeResult();
        var processedEntities = new List<CollectorCode>();

        // Get all existing collector codes for duplicate checking
        var existingCodes = await _repository.GetQuery()
            .Where(x => !x.IsDeleted)
            .ToListAsync(cancellationToken);

        foreach (var item in request.CollectorCodes)
        {
            var isNewRecord = string.IsNullOrEmpty(item.Id) || item.Id.StartsWith("gid");
            
            if (isNewRecord)
            {
                await ProcessCreateOperation(item, request.UserId, existingCodes, processedEntities, result, cancellationToken);
            }
            else
            {
                await ProcessUpdateOperation(item, request.UserId, existingCodes, processedEntities, result, cancellationToken);
            }
        }

        await _unitOfWork.SaveAsync(cancellationToken);
        result.Data = processedEntities;

        return result;
    }

    private async Task ProcessCreateOperation(
        CreateOrUpdateCollectorCodeItem item,
        string? userId,
        List<CollectorCode> existingCodes,
        List<CollectorCode> processedEntities,
        CreateOrUpdateCollectorCodeResult result,
        CancellationToken cancellationToken)
    {
        var duplicateExists = existingCodes.Any(x => x.CollectorCodeAssigned == item.CollectorCodeAssigned) ||
                             processedEntities.Any(x => x.CollectorCodeAssigned == item.CollectorCodeAssigned);

        if (duplicateExists)
        {
            throw new Exception($"Collector Code '{item.CollectorCodeAssigned}' already exists.");
        }

        var entity = new CollectorCode
        {
            CreatedById = userId,
            CollectorCodeAssigned = item.CollectorCodeAssigned,
            OutstationOrLocal = item.OutstationOrLocal,
            CalculationIndicator = item.CalculationIndicator,
            Rate = item.Rate,
            CollectorRefCodeId = item.CollectorRefCodeId
        };

        await _repository.CreateAsync(entity, cancellationToken);
        processedEntities.Add(entity);
        result.CreatedCount++;
    }

    private async Task ProcessUpdateOperation(
        CreateOrUpdateCollectorCodeItem item,
        string? userId,
        List<CollectorCode> existingCodes,
        List<CollectorCode> processedEntities,
        CreateOrUpdateCollectorCodeResult result,
        CancellationToken cancellationToken)
    {
        var entity = await _repository.GetAsync(item.Id ?? string.Empty, cancellationToken);
        
        if (entity == null)
        {
            throw new Exception($"Collector Code with ID '{item.Id}' not found.");
        }

        var duplicateExists = existingCodes.Any(x => x.CollectorCodeAssigned == item.CollectorCodeAssigned && x.Id != item.Id) ||
                             processedEntities.Any(x => x.CollectorCodeAssigned == item.CollectorCodeAssigned && x.Id != item.Id);

        if (duplicateExists)
        {
            throw new Exception($"Collector Code '{item.CollectorCodeAssigned}' already exists.");
        }

        entity.UpdatedById = userId;
        entity.CollectorCodeAssigned = item.CollectorCodeAssigned;
        entity.OutstationOrLocal = item.OutstationOrLocal;
        entity.CalculationIndicator = item.CalculationIndicator;
        entity.Rate = item.Rate;
        entity.CollectorRefCodeId = item.CollectorRefCodeId;

        _repository.Update(entity);
        processedEntities.Add(entity);
        result.UpdatedCount++;
    }
} 