﻿using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.CollectorCodeManager.Commands;

public class UpdateCollectorCodeResult
{
    public CollectorCode? Data { get; set; }
}

public class UpdateCollectorCodeRequest : IRequest<UpdateCollectorCodeResult>
{
    public string? Id { get; init; }
    public string? CollectorCodeAssigned { get; init; }
    public string? OutstationOrLocal { get; init; }
    public string? CalculationIndicator { get; init; }
    public decimal? Rate { get; init; }
    public string? CollectorRefCodeId { get; init; }
    public string? UpdatedById { get; init; }
}

public class UpdateCollectorCodeValidator : AbstractValidator<UpdateCollectorCodeRequest>
{
    public UpdateCollectorCodeValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
        RuleFor(x => x.CollectorCodeAssigned).NotEmpty();
        RuleFor(x => x.OutstationOrLocal).NotEmpty();
        RuleFor(x => x.CalculationIndicator).NotEmpty();
        RuleFor(x => x.Rate).NotEmpty();
        RuleFor(x => x.CollectorRefCodeId).NotEmpty();
    }
}

public class UpdateCollectorCodeHandler : IRequestHandler<UpdateCollectorCodeRequest, UpdateCollectorCodeResult>
{
    private readonly ICommandRepository<CollectorCode> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public UpdateCollectorCodeHandler(
        ICommandRepository<CollectorCode> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<UpdateCollectorCodeResult> Handle(UpdateCollectorCodeRequest request, CancellationToken cancellationToken)
    {
        // Check for duplicate collector code, excluding the current record being updated
        var existingCollectorCode = await _repository.GetQuery()
            .Where(x => x.CollectorCodeAssigned == request.CollectorCodeAssigned && 
                       x.Id != request.Id && 
                       !x.IsDeleted)
            .FirstOrDefaultAsync(cancellationToken);

        if (existingCollectorCode != null)
        {
            throw new Exception($"Collector Code :'{request.CollectorCodeAssigned}' already exists.");
        }

        var entity = await _repository.GetAsync(request.Id ?? string.Empty, cancellationToken);

        if (entity == null)
        {
            throw new Exception($"Entity not found: {request.Id}");
        }

        entity.UpdatedById = request.UpdatedById;

        entity.CollectorCodeAssigned = request.CollectorCodeAssigned;
        entity.OutstationOrLocal = request.OutstationOrLocal;
        entity.CalculationIndicator = request.CalculationIndicator;
        entity.Rate = request.Rate;
        entity.CollectorRefCodeId = request.CollectorRefCodeId;
        _repository.Update(entity);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new UpdateCollectorCodeResult
        {
            Data = entity
        };
    }
}

