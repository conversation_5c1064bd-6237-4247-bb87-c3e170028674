﻿using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;

namespace Application.Features.CollectorCodeManager.Commands;

public class DeleteCollectorCodeResult
{
    public CollectorCode? Data { get; set; }
}

public class DeleteCollectorCodeRequest : IRequest<DeleteCollectorCodeResult>
{
    public string? Id { get; init; }
    public string? DeletedById { get; init; }
}

public class DeleteCollectorCodeValidator : AbstractValidator<DeleteCollectorCodeRequest>
{
    public DeleteCollectorCodeValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
    }
}

public class DeleteCollectorCodeHandler : IRequestHandler<DeleteCollectorCodeRequest, DeleteCollectorCodeResult>
{
    private readonly ICommandRepository<CollectorCode> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public DeleteCollectorCodeHandler(
        ICommandRepository<CollectorCode> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<DeleteCollectorCodeResult> Handle(DeleteCollectorCodeRequest request, CancellationToken cancellationToken)
    {

        var entity = await _repository.GetAsync(request.Id ?? string.Empty, cancellationToken);

        if (entity == null)
        {
            throw new Exception($"Entity not found: {request.Id}");
        }

        entity.UpdatedById = request.DeletedById;

        _repository.Delete(entity);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new DeleteCollectorCodeResult
        {
            Data = entity
        };
    }
}

