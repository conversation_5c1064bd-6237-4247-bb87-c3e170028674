﻿using Application.Common.CQS.Queries;
using Application.Common.Extensions;
using AutoMapper;
using Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.CollectorCodeManager.Queries;

public record GetCollectorCodeListDto
{
    public string? Id { get; init; }
    public string? CollectorCodeAssigned { get; init; }
    public string? OutstationOrLocal { get; init; }
    public string? CalculationIndicator { get; init; }
    public decimal? Rate { get; init; }
    public string? CollectorRefCodeId { get; init; }
    public DateTime? CreatedAtUtc { get; init; }
}

public class GetCollectorCodeListProfile : Profile
{
    public GetCollectorCodeListProfile()
    {
        CreateMap<CollectorCode, GetCollectorCodeListDto>();
    }
}

public class GetCollectorCodeListResult
{
    public List<GetCollectorCodeListDto>? Data { get; init; }
}

public class GetCollectorCodeListRequest : IRequest<GetCollectorCodeListResult>
{
    public bool IsDeleted { get; init; } = false;
}


public class GetCollectorCodeListHandler : IRequestHandler<GetCollectorCodeListRequest, GetCollectorCodeListResult>
{
    private readonly IMapper _mapper;
    private readonly IQueryContext _context;

    public GetCollectorCodeListHandler(IMapper mapper, IQueryContext context)
    {
        _mapper = mapper;
        _context = context;
    }

    public async Task<GetCollectorCodeListResult> Handle(GetCollectorCodeListRequest request, CancellationToken cancellationToken)
    {
        var query = _context
            .CollectorCode
            .AsNoTracking()
            .IsDeletedEqualTo(request.IsDeleted)
            .AsQueryable();

        var entities = await query.ToListAsync(cancellationToken);

        var dtos = _mapper.Map<List<GetCollectorCodeListDto>>(entities);

        return new GetCollectorCodeListResult
        {
            Data = dtos
        };
    }


}



