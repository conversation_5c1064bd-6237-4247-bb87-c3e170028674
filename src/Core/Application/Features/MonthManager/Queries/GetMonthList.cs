﻿using Application.Common.CQS.Queries;
using Application.Common.Extensions;
using AutoMapper;
using Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.MonthManager.Queries;

public record GetMonthListDto
{
    public string? Id { get; init; }
    public int? MonthId { get; init; }
    public int? MonthStartNumber { get; init; }
    public int? MonthEndNumber { get; init; }
    public int? YearAssigned { get; init; }
    public DateTime? CreatedAtUtc { get; init; }
}

public class GetMonthListProfile : Profile
{
    public GetMonthListProfile()
    {
        CreateMap<Month, GetMonthListDto>();
    }
}

public class GetMonthListResult
{
    public List<GetMonthListDto>? Data { get; init; }
}

public class GetMonthListRequest : IRequest<GetMonthListResult>
{
    public bool IsDeleted { get; init; } = false;
}


public class GetMonthListHandler : IRequestHandler<GetMonthListRequest, GetMonthListResult>
{
    private readonly IMapper _mapper;
    private readonly IQueryContext _context;

    public GetMonthListHandler(IMapper mapper, IQueryContext context)
    {
        _mapper = mapper;
        _context = context;
    }

    public async Task<GetMonthListResult> Handle(GetMonthListRequest request, CancellationToken cancellationToken)
    {
        var query = _context
            .Month
            .AsNoTracking()
            .IsDeletedEqualTo(request.IsDeleted)
            .AsQueryable();

        var entities = await query.ToListAsync(cancellationToken);

        var dtos = _mapper.Map<List<GetMonthListDto>>(entities);

        return new GetMonthListResult
        {
            Data = dtos
        };
    }


}



