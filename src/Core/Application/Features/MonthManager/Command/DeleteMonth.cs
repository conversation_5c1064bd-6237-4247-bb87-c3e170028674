﻿using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;

namespace Application.Features.MonthManager.Commands;

public class DeleteMonthResult
{
    public Month? Data { get; set; }
}

public class DeleteMonthRequest : IRequest<DeleteMonthResult>
{
    public string? Id { get; init; }
    public string? DeletedById { get; init; }
}

public class DeleteMonthValidator : AbstractValidator<DeleteMonthRequest>
{
    public DeleteMonthValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
    }
}

public class DeleteMonthHandler : IRequestHandler<DeleteMonthRequest, DeleteMonthResult>
{
    private readonly ICommandRepository<Month> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public DeleteMonthHandler(
        ICommandRepository<Month> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<DeleteMonthResult> Handle(DeleteMonthRequest request, CancellationToken cancellationToken)
    {

        var entity = await _repository.GetAsync(request.Id ?? string.Empty, cancellationToken);

        if (entity == null)
        {
            throw new Exception($"Entity not found: {request.Id}");
        }

        entity.UpdatedById = request.DeletedById;

        _repository.Delete(entity);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new DeleteMonthResult
        {
            Data = entity
        };
    }
}

