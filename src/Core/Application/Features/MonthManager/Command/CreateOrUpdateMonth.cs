using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.MonthManager.Commands;

public class CreateOrUpdateMonthResult
{
    public List<Month> Data { get; set; } = new();
    public int CreatedCount { get; set; }
    public int UpdatedCount { get; set; }
}

public class CreateOrUpdateMonthItem
{
    public string? Id { get; set; }
    public int? MonthId { get; set; }
    public int? MonthStartNumber { get; set; }
    public int? MonthEndNumber { get; set; }
    public int? YearAssigned { get; set; }
}

public class CreateOrUpdateMonthRequest : IRequest<CreateOrUpdateMonthResult>
{
    public List<CreateOrUpdateMonthItem> Months { get; set; } = new();
    public string? UserId { get; set; }
}

public class CreateOrUpdateMonthValidator : AbstractValidator<CreateOrUpdateMonthRequest>
{
    public CreateOrUpdateMonthValidator()
    {
        RuleFor(x => x.Months).NotEmpty().WithMessage("At least one month is required.");
        RuleFor(x => x.UserId).NotEmpty().WithMessage("User ID is required.");
        
        RuleForEach(x => x.Months).ChildRules(item =>
        {
            item.RuleFor(x => x.MonthId).NotEmpty().WithMessage("Month ID is required.");
            item.RuleFor(x => x.MonthStartNumber).NotEmpty().WithMessage("Month Start Number is required.");
            item.RuleFor(x => x.MonthEndNumber).NotEmpty().WithMessage("Month End Number is required.");
            item.RuleFor(x => x.YearAssigned).NotEmpty().WithMessage("Year Assigned is required.");
        });
    }
}

public class CreateOrUpdateMonthHandler : IRequestHandler<CreateOrUpdateMonthRequest, CreateOrUpdateMonthResult>
{
    private readonly ICommandRepository<Month> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public CreateOrUpdateMonthHandler(
        ICommandRepository<Month> repository,
        IUnitOfWork unitOfWork)
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<CreateOrUpdateMonthResult> Handle(CreateOrUpdateMonthRequest request, CancellationToken cancellationToken = default)
    {
        var result = new CreateOrUpdateMonthResult();
        var processedEntities = new List<Month>();

        // Get all existing collector codes for duplicate checking
        var existingCodes = await _repository.GetQuery()
            .Where(x => !x.IsDeleted)
            .ToListAsync(cancellationToken);

        foreach (var item in request.Months)
        {
            var isNewRecord = string.IsNullOrEmpty(item.Id) || item.Id.StartsWith("gid");
            
            if (isNewRecord)
            {
                await ProcessCreateOperation(item, request.UserId, existingCodes, processedEntities, result, cancellationToken);
            }
            else
            {
                await ProcessUpdateOperation(item, request.UserId, existingCodes, processedEntities, result, cancellationToken);
            }
        }

        await _unitOfWork.SaveAsync(cancellationToken);
        result.Data = processedEntities;

        return result;
    }

    private async Task ProcessCreateOperation(
        CreateOrUpdateMonthItem item,
        string? userId,
        List<Month> existingCodes,
        List<Month> processedEntities,
        CreateOrUpdateMonthResult result,
        CancellationToken cancellationToken)
    {
        var duplicateExists = existingCodes.Any(x => x.MonthId == item.MonthId && x.YearAssigned == item.YearAssigned) ||
                             processedEntities.Any(x => x.MonthId == item.MonthId && x.YearAssigned == item.YearAssigned);

        if (duplicateExists)
        {
            throw new Exception($"Month '{item.MonthId}' already exists for year '{item.YearAssigned}'.");
        }

        var entity = new Month
        {
            CreatedById = userId,
            MonthId = item.MonthId,
            MonthStartNumber = item.MonthStartNumber,
            MonthEndNumber = item.MonthEndNumber,
            YearAssigned = item.YearAssigned
        };

        await _repository.CreateAsync(entity, cancellationToken);
        processedEntities.Add(entity);
        result.CreatedCount++;
    }

    private async Task ProcessUpdateOperation(
        CreateOrUpdateMonthItem item,
        string? userId,
        List<Month> existingCodes,
        List<Month> processedEntities,
        CreateOrUpdateMonthResult result,
        CancellationToken cancellationToken)
    {
        var entity = await _repository.GetAsync(item.Id ?? string.Empty, cancellationToken);
        
        if (entity == null)
        {
            throw new Exception($"Month with ID '{item.Id}' not found.");
        }

        var duplicateExists = existingCodes.Any(x => x.MonthId == item.MonthId && x.YearAssigned == item.YearAssigned && x.Id != item.Id) ||
                             processedEntities.Any(x => x.MonthId == item.MonthId && x.YearAssigned == item.YearAssigned && x.Id != item.Id);

        if (duplicateExists)
        {
            throw new Exception($"Month '{item.MonthId}' already exists for year '{item.YearAssigned}'.");
        }

        entity.UpdatedById = userId;
        entity.MonthId = item.MonthId;
        entity.MonthStartNumber = item.MonthStartNumber;
        entity.MonthEndNumber = item.MonthEndNumber;
        entity.YearAssigned = item.YearAssigned;

        _repository.Update(entity);
        processedEntities.Add(entity);
        result.UpdatedCount++;
    }
} 