using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;

namespace Application.Features.PaymentTypeManager.Commands;

public class UpdatePaymentTypeResult
{
    public PaymentType? Data { get; set; }
}

public class UpdatePaymentTypeRequest : IRequest<UpdatePaymentTypeResult>
{
    public string? Id { get; init; }
    public string? Code { get; init; }
    public string? Description { get; init; }
    public string? UpdatedById { get; init; }
    public bool IsActive { get; init; } = true;
}

public class UpdatePaymentTypeValidator : AbstractValidator<UpdatePaymentTypeRequest>
{
    public UpdatePaymentTypeValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
        RuleFor(x => x.Code).NotEmpty();
        RuleFor(x => x.Description).NotEmpty();
        RuleFor(x => x.IsActive).NotNull();
    }
}

public class UpdatePaymentTypeHandler : IRequestHandler<UpdatePaymentTypeRequest, UpdatePaymentTypeResult>
{
    private readonly ICommandRepository<PaymentType> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public UpdatePaymentTypeHandler(
        ICommandRepository<PaymentType> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<UpdatePaymentTypeResult> Handle(UpdatePaymentTypeRequest request, CancellationToken cancellationToken)
    {

        var entity = await _repository.GetAsync(request.Id ?? string.Empty, cancellationToken);

        if (entity == null)
        {
            throw new Exception($"Entity not found: {request.Id}");
        }

        entity.UpdatedById = request.UpdatedById;

        entity.Code = request.Code;
        entity.Description = request.Description;
        entity.IsActive = request.IsActive;

        _repository.Update(entity);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new UpdatePaymentTypeResult
        {
            Data = entity
        };
    }
}

