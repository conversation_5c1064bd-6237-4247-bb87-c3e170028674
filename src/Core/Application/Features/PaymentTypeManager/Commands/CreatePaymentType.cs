using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.PaymentTypeManager.Commands;

public class CreatePaymentTypeResult
{
    public PaymentType? Data { get; set; }
    
}

public class CreatePaymentTypeRequest : IRequest<CreatePaymentTypeResult>
{
    public string? Code { get; init; }
    public string? Description { get; init; }
    public bool IsActive { get; init; } = true;
    public string? CreatedById { get; init; }
}

public class CreatePaymentTypeValidator : AbstractValidator<CreatePaymentTypeRequest>
{
    public CreatePaymentTypeValidator()
    {
        RuleFor(x => x.Code).NotEmpty();
        RuleFor(x => x.Description).NotEmpty();
        RuleFor(x => x.IsActive).NotNull();
    }
}

public class CreatePaymentTypeHandler : IRequestHandler<CreatePaymentTypeRequest, CreatePaymentTypeResult>
{
    private readonly ICommandRepository<PaymentType> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public CreatePaymentTypeHandler(
        ICommandRepository<PaymentType> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<CreatePaymentTypeResult> Handle(CreatePaymentTypeRequest request, CancellationToken cancellationToken = default)
    {

        // Check for duplicate Product Code
        var existingPaymentType = await _repository.GetQuery()
            .Where(x => x.Code == request.Code && !x.IsDeleted)
            .FirstOrDefaultAsync(cancellationToken);

        if (existingPaymentType != null)
        {
            throw new Exception($"Payment Type Code :'{request.Code}' already exists.");
        }

        var existingDescription = await _repository.GetQuery()
            .Where(x => x.Description == request.Description && !x.IsDeleted)
            .FirstOrDefaultAsync(cancellationToken);

        if (existingDescription != null)
        {
            throw new Exception($"Description :'{request.Description}' already exists.");
        }

        var entity = new PaymentType();
        entity.CreatedById = request.CreatedById;

        entity.Code = request.Code;
        entity.Description = request.Description;
        entity.IsActive = request.IsActive;

        await _repository.CreateAsync(entity, cancellationToken);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new CreatePaymentTypeResult
        {
            Data = entity
        };
    }
}