using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;

namespace Application.Features.DivisionManager.Commands;

public class UpdateDivisionResult
{
    public Division? Data { get; set; }
}

public class UpdateDivisionRequest : IRequest<UpdateDivisionResult>
{
    public string? Id { get; init; }
    public string? DivisionCode { get; init; }
    public string? Description { get; init; }
    public string? UpdatedById { get; init; }
}

public class UpdateDivisionValidator : AbstractValidator<UpdateDivisionRequest>
{
    public UpdateDivisionValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
        RuleFor(x => x.DivisionCode).NotEmpty();
        RuleFor(x => x.Description).NotEmpty();
    }
}

public class UpdateDivisionHandler : IRequestHandler<UpdateDivisionRequest, UpdateDivisionResult>
{
    private readonly ICommandRepository<Division> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public UpdateDivisionHandler(
        ICommandRepository<Division> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<UpdateDivisionResult> Handle(UpdateDivisionRequest request, CancellationToken cancellationToken)
    {
        var entity = await _repository.GetAsync(request.Id ?? string.Empty, cancellationToken);

        if (entity == null)
        {
            throw new Exception($"Entity not found: {request.Id}");
        }

        entity.UpdatedById = request.UpdatedById;

        entity.DivisionCode = request.DivisionCode;
        entity.Description = request.Description;

        _repository.Update(entity);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new UpdateDivisionResult
        {
            Data = entity
        };
    }
} 