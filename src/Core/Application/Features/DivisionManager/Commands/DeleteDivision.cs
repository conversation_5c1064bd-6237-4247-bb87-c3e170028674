using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;

namespace Application.Features.DivisionManager.Commands;

public class DeleteDivisionResult
{
    public Division? Data { get; set; }
}

public class DeleteDivisionRequest : IRequest<DeleteDivisionResult>
{
    public string? Id { get; init; }
    public string? DeletedById { get; init; }
}

public class DeleteDivisionValidator : AbstractValidator<DeleteDivisionRequest>
{
    public DeleteDivisionValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
    }
}

public class DeleteDivisionHandler : IRequestHandler<DeleteDivisionRequest, DeleteDivisionResult>
{
    private readonly ICommandRepository<Division> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public DeleteDivisionHandler(
        ICommandRepository<Division> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<DeleteDivisionResult> Handle(DeleteDivisionRequest request, CancellationToken cancellationToken)
    {
        var entity = await _repository.GetAsync(request.Id ?? string.Empty, cancellationToken);

        if (entity == null)
        {
            throw new Exception($"Entity not found: {request.Id}");
        }

        entity.UpdatedById = request.DeletedById;

        _repository.Delete(entity);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new DeleteDivisionResult
        {
            Data = entity
        };
    }
} 