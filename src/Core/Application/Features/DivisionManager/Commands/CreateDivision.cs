using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.DivisionManager.Commands;

public class CreateDivisionResult
{
    public Division? Data { get; set; }
}

public class CreateDivisionRequest : IRequest<CreateDivisionResult>
{
    public string? DivisionCode { get; init; }
    public string? Description { get; init; }
    public string? CreatedById { get; init; }
}

public class CreateDivisionValidator : AbstractValidator<CreateDivisionRequest>
{
    public CreateDivisionValidator()
    {
        RuleFor(x => x.DivisionCode).NotEmpty();
        RuleFor(x => x.Description).NotEmpty();
    }
}

public class CreateDivisionHandler : IRequestHandler<CreateDivisionRequest, CreateDivisionResult>
{
    private readonly ICommandRepository<Division> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public CreateDivisionHandler(
        ICommandRepository<Division> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<CreateDivisionResult> Handle(CreateDivisionRequest request, CancellationToken cancellationToken = default)
    {
        // Check for duplicate DivisionCode
        var existingDivision = await _repository.GetQuery()
            .Where(x => x.DivisionCode == request.DivisionCode && !x.IsDeleted)
            .FirstOrDefaultAsync(cancellationToken);

        if (existingDivision != null)
        {
            throw new Exception($"Division Code :'{request.DivisionCode}' already exists.");
        }

        var entity = new Division();
        entity.CreatedById = request.CreatedById;

        entity.DivisionCode = request.DivisionCode;
        entity.Description = request.Description;

        await _repository.CreateAsync(entity, cancellationToken);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new CreateDivisionResult
        {
            Data = entity
        };
    }
} 