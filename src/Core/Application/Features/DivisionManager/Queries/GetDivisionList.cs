using Application.Common.CQS.Queries;
using Application.Common.Extensions;
using AutoMapper;
using Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.DivisionManager.Queries;

public record GetDivisionListDto
{
    public string? Id { get; init; }
    public string? DivisionCode { get; init; }
    public string? Description { get; init; }
    public DateTime? CreatedAtUtc { get; init; }
}

public class GetDivisionListProfile : Profile
{
    public GetDivisionListProfile()
    {
        CreateMap<Division, GetDivisionListDto>();
    }
}

public class GetDivisionListResult
{
    public List<GetDivisionListDto>? Data { get; init; }
}

public class GetDivisionListRequest : IRequest<GetDivisionListResult>
{
    public bool IsDeleted { get; init; } = false;
}

public class GetDivisionListHandler : IRequestHandler<GetDivisionListRequest, GetDivisionListResult>
{
    private readonly IMapper _mapper;
    private readonly IQueryContext _context;

    public GetDivisionListHandler(IMapper mapper, IQueryContext context)
    {
        _mapper = mapper;
        _context = context;
    }

    public async Task<GetDivisionListResult> Handle(GetDivisionListRequest request, CancellationToken cancellationToken)
    {
        var query = _context
            .Division
            .AsNoTracking()
            .IsDeletedEqualTo(request.IsDeleted)
            .AsQueryable();

        var entities = await query.ToListAsync(cancellationToken);

        var dtos = _mapper.Map<List<GetDivisionListDto>>(entities);

        return new GetDivisionListResult
        {
            Data = dtos
        };
    }
} 