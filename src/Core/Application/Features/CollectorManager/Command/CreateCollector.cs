﻿using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;


namespace Application.Features.CollectorManager.Commands;

public class CreateCollectorResult
{
    public Collector? Data { get; set; }
}

public class CreateCollectorRequest : IRequest<CreateCollectorResult>
{
    public string? CollectorId { get; init; }
    public string? Name { get; init; }
    public string? Address1 { get; init; }
    public string? Address2 { get; init; }
    public string? City { get; init; }
    public string? State { get; init; }
    public string? PostCode { get; init; }
    public string? NricNo { get; init; }
    public bool IsActive { get; set; } = true;
    public string? PhoneNo { get; init; }
    public decimal? ReservedPercentage { get; init; }
    public decimal? ReservedCapping { get; init; }
    public string? BankName { get; init; }
    public string? BankAccountNo { get; init; }
    public string? TinNo { get; init; }
    public string? CollectorTypeCode { get; init; }
    public DateTime? DateJoined { get; init; }
    public DateTime? DateResigned { get; init; }
    public string? CreatedById { get; init; }
}

public class CreateCollectorValidator : AbstractValidator<CreateCollectorRequest>
{
    public CreateCollectorValidator()
    {
        RuleFor(x => x.CollectorId).NotEmpty();
        RuleFor(x => x.Name).NotEmpty();
        RuleFor(x => x.Address1).NotEmpty();
        RuleFor(x => x.Address2).NotEmpty();
        RuleFor(x => x.City).NotEmpty();
        RuleFor(x => x.State).NotEmpty();
        RuleFor(x => x.PostCode).NotEmpty();
        RuleFor(x => x.NricNo).NotEmpty();
        RuleFor(x => x.IsActive).NotNull();
        RuleFor(x => x.PhoneNo).NotEmpty();
        RuleFor(x => x.ReservedPercentage).NotEmpty();
        RuleFor(x => x.ReservedCapping).NotEmpty();
        RuleFor(x => x.BankName).NotEmpty();
        RuleFor(x => x.BankAccountNo).NotEmpty();
        RuleFor(x => x.TinNo).NotEmpty();
        RuleFor(x => x.CollectorTypeCode).NotEmpty();
        RuleFor(x => x.DateJoined).NotEmpty();
    }
}

public class CreateCollectorHandler : IRequestHandler<CreateCollectorRequest, CreateCollectorResult>
{
    private readonly ICommandRepository<Collector> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public CreateCollectorHandler(
        ICommandRepository<Collector> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<CreateCollectorResult> Handle(CreateCollectorRequest request, CancellationToken cancellationToken = default)
    {
        var collectorRefCodeExists = await _repository.GetQuery()
            .AnyAsync(x => !x.IsDeleted && x.CollectorId == request.CollectorId, cancellationToken);

        if (collectorRefCodeExists)
            throw new Exception($"Collector Code '{request.CollectorId}' already exists.");

        var nricExists = await _repository.GetQuery()
            .AnyAsync(x => !x.IsDeleted && x.NricNo == request.NricNo, cancellationToken);

        if (nricExists)
            throw new Exception($"NRIC No '{request.NricNo}' is already taken by another collector.");

        var entity = new Collector();
        entity.CreatedById = request.CreatedById;

        entity.CollectorId = request.CollectorId;
        entity.Name = request.Name;
        entity.Address1 = request.Address1;
        entity.Address2 = request.Address2;
        entity.City = request.City;
        entity.State = request.State;
        entity.PostCode = request.PostCode;
        entity.NricNo = request.NricNo;
        entity.PhoneNo = request.PhoneNo;
        entity.ReservedPercentage = request.ReservedPercentage;
        entity.ReservedCapping = request.ReservedCapping;
        entity.BankName = request.BankName;
        entity.BankAccountNo = request.BankAccountNo;
        entity.TinNo = request.TinNo;
        entity.CollectorTypeCode = request.CollectorTypeCode;
        entity.DateJoined = request.DateJoined;
        entity.DateResigned = request.DateResigned;
        entity.IsActive = request.IsActive;

        await _repository.CreateAsync(entity, cancellationToken);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new CreateCollectorResult
        {
            Data = entity
        };
    }
}