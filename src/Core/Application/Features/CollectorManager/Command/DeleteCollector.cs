﻿using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;

namespace Application.Features.CollectorManager.Commands;

public class DeleteCollectorResult
{
    public Collector? Data { get; set; }
}

public class DeleteCollectorRequest : IRequest<DeleteCollectorResult>
{
    public string? Id { get; init; }
    public string? DeletedById { get; init; }
}

public class DeleteCollectorValidator : AbstractValidator<DeleteCollectorRequest>
{
    public DeleteCollectorValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
    }
}

public class DeleteCollectorHandler : IRequestHandler<DeleteCollectorRequest, DeleteCollectorResult>
{
    private readonly ICommandRepository<Collector> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public DeleteCollectorHandler(
        ICommandRepository<Collector> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<DeleteCollectorResult> Handle(DeleteCollectorRequest request, CancellationToken cancellationToken)
    {

        var entity = await _repository.GetAsync(request.Id ?? string.Empty, cancellationToken);

        if (entity == null)
        {
            throw new Exception($"Entity not found: {request.Id}");
        }

        entity.UpdatedById = request.DeletedById;

        _repository.Delete(entity);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new DeleteCollectorResult
        {
            Data = entity
        };
    }
}

