﻿using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.CollectorManager.Commands;

public class UpdateCollectorResult
{
    public Collector? Data { get; set; }
}

public class UpdateCollectorRequest : IRequest<UpdateCollectorResult>
{
    public string? Id { get; init; }
    public string? CollectorId { get; init; }
    public string? Name { get; init; }
    public string? Address1 { get; init; }
    public string? Address2 { get; init; }
    public string? City { get; init; }
    public string? State { get; init; }
    public string? PostCode { get; init; }
    public string? NricNo { get; init; }
    public bool IsActive { get; set; } = true;
    public decimal? ReservedPercentage { get; init; }
    public decimal? ReservedCapping { get; init; }
    public string? PhoneNo { get; init; }
    public string? BankName { get; init; }
    public string? BankAccountNo { get; init; }
    public string? TinNo { get; init; }
    public string? CollectorTypeCode { get; init; }
    public DateTime? DateJoined { get; init; }
    public DateTime? DateResigned { get; init; }
    public string? UpdatedById { get; init; }
}

public class UpdateCollectorValidator : AbstractValidator<UpdateCollectorRequest>
{
    public UpdateCollectorValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
        RuleFor(x => x.CollectorId).NotEmpty();
        RuleFor(x => x.Name).NotEmpty();
        RuleFor(x => x.Address1).NotEmpty();
        RuleFor(x => x.Address2).NotEmpty();
        RuleFor(x => x.City).NotEmpty();
        RuleFor(x => x.State).NotEmpty();
        RuleFor(x => x.PostCode).NotEmpty();
        RuleFor(x => x.NricNo).NotEmpty();
        RuleFor(x => x.IsActive).NotNull();
        RuleFor(x => x.PhoneNo).NotEmpty();
        RuleFor(x => x.ReservedPercentage).NotEmpty();
        RuleFor(x => x.ReservedCapping).NotEmpty();
        RuleFor(x => x.BankName).NotEmpty();
        RuleFor(x => x.BankAccountNo).NotEmpty();
        RuleFor(x => x.TinNo).NotEmpty();
        RuleFor(x => x.CollectorTypeCode).NotEmpty();
        RuleFor(x => x.DateJoined).NotEmpty();
    }
}

public class UpdateCollectorHandler : IRequestHandler<UpdateCollectorRequest, UpdateCollectorResult>
{
    private readonly ICommandRepository<Collector> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public UpdateCollectorHandler(
        ICommandRepository<Collector> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<UpdateCollectorResult> Handle(UpdateCollectorRequest request, CancellationToken cancellationToken)
    {

        var entity = await _repository.GetAsync(request.Id ?? string.Empty, cancellationToken);

        if (entity == null)
        {
            throw new Exception($"Entity not found: {request.Id}");
        }

        entity.UpdatedById = request.UpdatedById;

        entity.CollectorId = request.CollectorId;
        entity.Name = request.Name;
        entity.Address1 = request.Address1;
        entity.Address2 = request.Address2;
        entity.City = request.City;
        entity.State = request.State;
        entity.PostCode = request.PostCode;
        entity.NricNo = request.NricNo;
        entity.PhoneNo = request.PhoneNo;
        entity.ReservedPercentage = request.ReservedPercentage;
        entity.ReservedCapping = request.ReservedCapping;
        entity.BankName = request.BankName;
        entity.BankAccountNo = request.BankAccountNo;
        entity.TinNo = request.TinNo;
        entity.CollectorTypeCode = request.CollectorTypeCode;
        entity.DateJoined = request.DateJoined;
        entity.DateResigned = request.DateResigned;
        entity.IsActive = request.IsActive;

        _repository.Update(entity);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new UpdateCollectorResult
        {
            Data = entity
        };
    }
}

