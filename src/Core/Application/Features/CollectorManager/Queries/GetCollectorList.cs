﻿using Application.Common.CQS.Queries;
using Application.Common.Extensions;
using AutoMapper;
using Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.CollectorManager.Queries;

public record GetCollectorListDto
{
    public string? Id { get; init; }
    public string? CollectorId { get; init; }
    public string? Name { get; init; }
    public string? Address1 { get; init; }
    public string? Address2 { get; init; }
    public string? City { get; init; }
    public string? State { get; init; }
    public string? PostCode { get; init; }
    public string? NricNo { get; init; }
    public bool IsActive { get; set; } = true;
    public string? PhoneNo { get; init; }
    public decimal? ReservedPercentage { get; init; }
    public decimal? ReservedCapping { get; init; }
    public string? BankName { get; init; }
    public string? BankAccountNo { get; init; }
    public string? TinNo { get; init; }
    public string? CollectorTypeCode { get; init; }
    public DateTime? DateJoined { get; init; }
    public DateTime? DateResigned { get; init; }
    public string? CreatedById { get; init; }
    public DateTime? CreatedAtUtc { get; init; }
}

public class GetCollectorListProfile : Profile
{
    public GetCollectorListProfile()
    {
        CreateMap<Collector, GetCollectorListDto>();
    }
}

public class GetCollectorListResult
{
    public List<GetCollectorListDto>? Data { get; init; }
}

public class GetCollectorListRequest : IRequest<GetCollectorListResult>
{
    public bool IsDeleted { get; init; } = false;
}


public class GetCollectorListHandler : IRequestHandler<GetCollectorListRequest, GetCollectorListResult>
{
    private readonly IMapper _mapper;
    private readonly IQueryContext _context;

    public GetCollectorListHandler(IMapper mapper, IQueryContext context)
    {
        _mapper = mapper;
        _context = context;
    }

    public async Task<GetCollectorListResult> Handle(GetCollectorListRequest request, CancellationToken cancellationToken)
    {
        var query = _context
            .Collector
            .AsNoTracking()
            .IsDeletedEqualTo(request.IsDeleted)
            .AsQueryable();

        var entities = await query.ToListAsync(cancellationToken);

        var dtos = _mapper.Map<List<GetCollectorListDto>>(entities);

        return new GetCollectorListResult
        {
            Data = dtos
        };
    }


}



