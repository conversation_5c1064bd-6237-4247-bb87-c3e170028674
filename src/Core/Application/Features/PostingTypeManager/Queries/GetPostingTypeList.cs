﻿using Application.Common.CQS.Queries;
using Application.Common.Extensions;
using AutoMapper;
using Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.PostingTypeManager.Queries;

public record GetPostingTypeListDto
{
    public string? Id { get; init; }
    public string? Code { get; init; }
    public string? Description { get; init; }
    public string? BalanceAdjustment { get; init; }
    public bool PostToOtherModule { get; init; }
    public DateTime? CreatedAtUtc { get; init; }
}

public class GetPostingTypeListProfile : Profile
{
    public GetPostingTypeListProfile()
    {
        CreateMap<PostingType, GetPostingTypeListDto>();
    }
}

public class GetPostingTypeListResult
{
    public List<GetPostingTypeListDto>? Data { get; init; }
}

public class GetPostingTypeListRequest : IRequest<GetPostingTypeListResult>
{
    public bool IsDeleted { get; init; } = false;
}


public class GetPostingTypeListHandler : IRequestHandler<GetPostingTypeListRequest, GetPostingTypeListResult>
{
    private readonly IMapper _mapper;
    private readonly IQueryContext _context;

    public GetPostingTypeListHandler(IMapper mapper, IQueryContext context)
    {
        _mapper = mapper;
        _context = context;
    }

    public async Task<GetPostingTypeListResult> Handle(GetPostingTypeListRequest request, CancellationToken cancellationToken)
    {
        var query = _context
            .PostingType
            .AsNoTracking()
            .IsDeletedEqualTo(request.IsDeleted)
            .AsQueryable();

        var entities = await query.ToListAsync(cancellationToken);

        var dtos = _mapper.Map<List<GetPostingTypeListDto>>(entities);

        return new GetPostingTypeListResult
        {
            Data = dtos
        };
    }


}



