﻿using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;

namespace Application.Features.PostingTypeManager.Commands;

public class UpdatePostingTypeResult
{
    public PostingType? Data { get; set; }
}

public class UpdatePostingTypeRequest : IRequest<UpdatePostingTypeResult>
{
    public string? Id { get; init; }
    public string? Code { get; init; }
    public string? Description { get; init; }
    public string? BalanceAdjustment { get; init; }
    public bool PostToOtherModule { get; init; } = true;
    public string? UpdatedById { get; init; }
}

public class UpdatePostingTypeValidator : AbstractValidator<UpdatePostingTypeRequest>
{
    public UpdatePostingTypeValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
        RuleFor(x => x.Code).NotEmpty();
        RuleFor(x => x.Description).NotEmpty();
        RuleFor(x => x.BalanceAdjustment).NotEmpty();
        RuleFor(x => x.PostToOtherModule).NotNull();
    }
}

public class UpdatePostingTypeHandler : IRequestHandler<UpdatePostingTypeRequest, UpdatePostingTypeResult>
{
    private readonly ICommandRepository<PostingType> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public UpdatePostingTypeHandler(
        ICommandRepository<PostingType> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<UpdatePostingTypeResult> Handle(UpdatePostingTypeRequest request, CancellationToken cancellationToken)
    {

        var entity = await _repository.GetAsync(request.Id ?? string.Empty, cancellationToken);

        if (entity == null)
        {
            throw new Exception($"Entity not found: {request.Id}");
        }

        entity.UpdatedById = request.UpdatedById;

        entity.Code = request.Code;
        entity.Description = request.Description;
        entity.BalanceAdjustment = request.BalanceAdjustment;
        entity.PostToOtherModule = request.PostToOtherModule;
        _repository.Update(entity);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new UpdatePostingTypeResult
        {
            Data = entity
        };
    }
}

