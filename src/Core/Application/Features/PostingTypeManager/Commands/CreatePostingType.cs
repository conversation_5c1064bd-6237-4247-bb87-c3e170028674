﻿using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.PostingTypeManager.Commands;

public class CreatePostingTypeResult
{
    public PostingType? Data { get; set; }
    
}

public class CreatePostingTypeRequest : IRequest<CreatePostingTypeResult>
{
    public string? Code { get; init; }
    public string? Description { get; init; }
    public string? BalanceAdjustment { get; init; }
    public bool PostToOtherModule { get; init; } = true;
    public string? CreatedById { get; init; }
}

public class CreatePostingTypeValidator : AbstractValidator<CreatePostingTypeRequest>
{
    public CreatePostingTypeValidator()
    {
        RuleFor(x => x.Code).NotEmpty();
        RuleFor(x => x.Description).NotEmpty();
        RuleFor(x => x.BalanceAdjustment).NotEmpty();
        RuleFor(x => x.PostToOtherModule).NotNull();
    }
}

public class CreatePostingTypeHandler : IRequestHandler<CreatePostingTypeRequest, CreatePostingTypeResult>
{
    private readonly ICommandRepository<PostingType> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public CreatePostingTypeHandler(
        ICommandRepository<PostingType> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<CreatePostingTypeResult> Handle(CreatePostingTypeRequest request, CancellationToken cancellationToken = default)
    {

        // Check for duplicate Product Code
        var existingPostingType = await _repository.GetQuery()
            .Where(x => x.Code == request.Code && !x.IsDeleted)
            .FirstOrDefaultAsync(cancellationToken);

        if (existingPostingType != null)
        {
            throw new Exception($"PostingType Code :'{request.Code}' already exists.");
        }

        var existingDescription = await _repository.GetQuery()
            .Where(x => x.Description == request.Description && !x.IsDeleted)
            .FirstOrDefaultAsync(cancellationToken);

        if (existingDescription != null)
        {
            throw new Exception($"Description :'{request.Description}' already exists.");
        }

        var entity = new PostingType();
        entity.CreatedById = request.CreatedById;

        entity.Code = request.Code;
        entity.Description = request.Description;
        entity.BalanceAdjustment = request.BalanceAdjustment;
        entity.PostToOtherModule = request.PostToOtherModule;

        await _repository.CreateAsync(entity, cancellationToken);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new CreatePostingTypeResult
        {
            Data = entity
        };
    }
}