﻿using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;

namespace Application.Features.PostingTypeManager.Commands;

public class DeletePostingTypeResult
{
    public PostingType? Data { get; set; }
}

public class DeletePostingTypeRequest : IRequest<DeletePostingTypeResult>
{
    public string? Id { get; init; }
    public string? DeletedById { get; init; }
}

public class DeletePostingTypeValidator : AbstractValidator<DeletePostingTypeRequest>
{
    public DeletePostingTypeValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
    }
}

public class DeletePostingTypeHandler : IRequestHandler<DeletePostingTypeRequest, DeletePostingTypeResult>
{
    private readonly ICommandRepository<PostingType> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public DeletePostingTypeHandler(
        ICommandRepository<PostingType> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<DeletePostingTypeResult> Handle(DeletePostingTypeRequest request, CancellationToken cancellationToken)
    {

        var entity = await _repository.GetAsync(request.Id ?? string.Empty, cancellationToken);

        if (entity == null)
        {
            throw new Exception($"Entity not found: {request.Id}");
        }

        entity.UpdatedById = request.DeletedById;

        _repository.Delete(entity);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new DeletePostingTypeResult
        {
            Data = entity
        };
    }
}

