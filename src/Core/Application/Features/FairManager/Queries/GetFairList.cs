﻿using Application.Common.CQS.Queries;
using Application.Common.Extensions;
using AutoMapper;
using Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.FairManager.Queries;

public record GetFairListDto
{
    public string? Id { get; init; }
    public string? FairCode { get; init; }
    public string? Description { get; init; }
    public DateTime? CreatedAtUtc { get; init; }
}

public class GetFairListProfile : Profile
{
    public GetFairListProfile()
    {
        CreateMap<Fair, GetFairListDto>();
    }
}

public class GetFairListResult
{
    public List<GetFairListDto>? Data { get; init; }
}

public class GetFairListRequest : IRequest<GetFairListResult>
{
    public bool IsDeleted { get; init; } = false;
}


public class GetFairListHandler : IRequestHandler<GetFairListRequest, GetFairListResult>
{
    private readonly IMapper _mapper;
    private readonly IQueryContext _context;

    public GetFairListHandler(IMapper mapper, IQueryContext context)
    {
        _mapper = mapper;
        _context = context;
    }

    public async Task<GetFairListResult> Handle(GetFairListRequest request, CancellationToken cancellationToken)
    {
        var query = _context
            .Fair
            .AsNoTracking()
            .IsDeletedEqualTo(request.IsDeleted)
            .AsQueryable();

        var entities = await query.ToListAsync(cancellationToken);

        var dtos = _mapper.Map<List<GetFairListDto>>(entities);

        return new GetFairListResult
        {
            Data = dtos
        };
    }


}



