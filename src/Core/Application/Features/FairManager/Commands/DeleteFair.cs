﻿using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.FairManager.Commands;

public class DeleteFairResult
{
    public Fair? Data { get; set; }
}

public class DeleteFairRequest : IRequest<DeleteFairResult>
{
    public string? Id { get; init; }
    public string? DeletedById { get; init; }
}

public class DeleteFairValidator : AbstractValidator<DeleteFairRequest>
{
    public DeleteFairValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
    }
}

public class DeleteFairHandler : IRequestHandler<DeleteFairRequest, DeleteFairResult>
{
    private readonly ICommandRepository<Fair> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public DeleteFairHandler(
        ICommandRepository<Fair> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<DeleteFairResult> Handle(DeleteFairRequest request, CancellationToken cancellationToken)
    {
        var entity = await _repository.GetAsync(request.Id ?? string.Empty, cancellationToken);

        if (entity == null)
        {
            throw new Exception($"Entity not found: {request.Id}");
        }

        entity.UpdatedById = request.DeletedById;

        _repository.Delete(entity);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new DeleteFairResult
        {
            Data = entity
        };
    }
}

