﻿using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;

namespace Application.Features.FairManager.Commands;

public class UpdateFairResult
{
    public Fair? Data { get; set; }
}

public class UpdateFairRequest : IRequest<UpdateFairResult>
{
    public string? Id { get; init; }
    public string? FairCode { get; init; }
    public string? Description { get; init; }
    public string? UpdatedById { get; init; }
}

public class UpdateFairValidator : AbstractValidator<UpdateFairRequest>
{
    public UpdateFairValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
        RuleFor(x => x.FairCode).NotEmpty();
        RuleFor(x => x.Description).NotEmpty();

    }
}

public class UpdateFairHandler : IRequestHandler<UpdateFairRequest, UpdateFairResult>
{
    private readonly ICommandRepository<Fair> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public UpdateFairHandler(
        ICommandRepository<Fair> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<UpdateFairResult> Handle(UpdateFairRequest request, CancellationToken cancellationToken)
    {

        var entity = await _repository.GetAsync(request.Id ?? string.Empty, cancellationToken);

        if (entity == null)
        {
            throw new Exception($"Entity not found: {request.Id}");
        }

        entity.UpdatedById = request.UpdatedById;

        entity.FairCode = request.FairCode;
        entity.Description = request.Description;

        _repository.Update(entity);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new UpdateFairResult
        {
            Data = entity
        };
    }
}

