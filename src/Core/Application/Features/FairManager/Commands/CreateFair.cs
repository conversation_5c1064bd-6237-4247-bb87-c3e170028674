﻿using Application.Common.Repositories;
using Domain.Entities;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.FairManager.Commands;

public class CreateFairResult
{
    public Fair? Data { get; set; }
}

public class CreateFairRequest : IRequest<CreateFairResult>
{
    public string? FairCode { get; init; }
    public string? Description { get; init; }
    public string? CreatedById { get; init; }
}

public class CreateFairValidator : AbstractValidator<CreateFairRequest>
{
    public CreateFairValidator()
    {
        RuleFor(x => x.FairCode).NotEmpty();
        RuleFor(x => x.Description).NotEmpty();
    }
}

public class CreateFairHandler : IRequestHandler<CreateFairRequest, CreateFairResult>
{
    private readonly ICommandRepository<Fair> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public CreateFairHandler(
        ICommandRepository<Fair> repository,
        IUnitOfWork unitOfWork
        )
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<CreateFairResult> Handle(CreateFairRequest request, CancellationToken cancellationToken = default)
    {
        var existingFair = await _repository.GetQuery()
            .Where(x => x.FairCode == request.FairCode && !x.IsDeleted)
            .FirstOrDefaultAsync(cancellationToken);

        if (existingFair != null)
        {
            throw new Exception($"Fair Code :'{request.FairCode}' already exists.");
        }
        
        var entity = new Fair();
        entity.CreatedById = request.CreatedById;

        entity.FairCode = request.FairCode;
        entity.Description = request.Description;

        await _repository.CreateAsync(entity, cancellationToken);
        await _unitOfWork.SaveAsync(cancellationToken);

        return new CreateFairResult
        {
            Data = entity
        };
    }
}