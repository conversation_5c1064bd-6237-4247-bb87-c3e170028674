using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Domain.Common;

namespace Domain.Entities
{
    public class SalesCommissionChargebackRate : BaseEntity
    {
        public string? CancellationPeriod { get; set; }
        public string? CollectionRange { get; set; }
        public decimal? CollectionRate { get; set; }
        public decimal? ChargebackRate { get; set; }
        public string? ChargebackInterval { get; set; }
        public string? SalesCommissionChargebackRateCode { get; set; }
        public bool IsActive { get; set; } = true;

    }
}
