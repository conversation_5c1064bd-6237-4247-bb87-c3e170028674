using ASPNET.BackEnd;
using ASPNET.BackEnd.Common.Middlewares;
using ASPNET.FrontEnd;

namespace ASPNET
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddRazorPages()
                .AddRazorPagesOptions(options =>
                {
                    options.Conventions.AddPageRoute("/Login", ""); // Set Login.cshtml as default page
                });

            services.AddBackEndServices(Configuration);
            services.AddFrontEndServices();
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            app.Use(async (context, next) =>
            {
                context.Request.EnableBuffering();
                var reader = new StreamReader(context.Request.Body, leaveOpen: true);
                var body = await reader.ReadToEndAsync();
                context.Request.Body.Position = 0;

                Console.WriteLine($"Incoming Request: {body}");
                await next();
            });

            app.RegisterBackEndBuilder(env, Configuration);

            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }
            else
            {
                app.UseExceptionHandler("/Error");
                // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
                app.UseHsts();
            }

            app.UseHttpsRedirection();
            app.UseStaticFiles();
            app.UseRouting();
            app.UseCors();
            app.UseMiddleware<GlobalApiExceptionHandlerMiddleware>();
            app.UseAuthentication();
            app.UseAuthorization();

            // Root and Index path redirect
            app.Use(async (context, next) =>
            {
                var path = context.Request.Path.Value?.ToLower();

                if (path == "/" || path == "/index" || path == "/index/")
                {
                    context.Response.Redirect("/Accounts/Login");
                    return;
                }

                await next();
            });

            // Endpoint mapping
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapStaticAssets();
                endpoints.MapFrontEndRoutes();
                endpoints.MapBackEndRoutes();
            });

            Console.WriteLine($"Environment: {env.EnvironmentName}");
        }
    }
}
