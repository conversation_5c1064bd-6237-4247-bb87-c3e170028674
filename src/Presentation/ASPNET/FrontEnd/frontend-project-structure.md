# Frontend Project Structure (`src/Presentation/ASPNET/FrontEnd/Pages/`)

This structure outlines the organization of the ASP.NET + Vue frontend by feature and shared responsibility.

## 📁 Region/
Feature-specific folder for Region-related functionality.

```
src/Presentation/ASPNET/FrontEnd/Pages/Region/
├── RegionList.cshtml               # Razor page markup
├── RegionList.cshtml.js           # Vue Composition API logic for RegionList
├── Services/
│   └── RegionService.js           # Handles all Region-related API calls
└── Components/                    # Vue components scoped to the Region feature
    ├── ContractTab.js
    ├── CollectionTab.js
    ├── CustomerTab.js
    ├── ProductGrid.js
    └── CommentGrid.js
```

---

## 📁 Shared/
Shared components and utilities reusable across features.

```
src/Presentation/ASPNET/FrontEnd/Pages/Shared/
├── Components/                    # Global UI components
│   ├── FormComponents.js          # Common form inputs (text fields, dropdowns, etc.)
│   ├── GridComponents.js          # Reusable table/grid UI elements
│   └── ModalComponents.js         # Standard modal dialogs
└── Utils/                         # Common utility functions
    ├── ValidationUtils.js         # Input validation helpers
    └── FormatUtils.js             # Data formatting helpers (dates, numbers, etc.)
```

> 🔁 Follow feature-based modular structure: Each major feature has its own folder for separation of concern. Shared components and utilities are kept in centralized directories.

## 📦 Reference Path Examples (refer format ONLY)

### CustomerContractEntryList.cshtml

```html
@section scripts {
    <!-- Page-specific components -->
    <script src="~/FrontEnd/Pages/CustomerContractEntries/components/ContractTab.js"></script>
    <script src="~/FrontEnd/Pages/CustomerContractEntries/components/ProductGrid.js"></script>

    <!-- Shared components -->
    <script src="~/FrontEnd/Pages/Shared/components/FormComponents.js"></script>

    <!-- Main page script -->
    <script src="~/FrontEnd/Pages/CustomerContractEntries/CustomerContractEntryList.cshtml.js"></script>
}
```

### BankList.cshtml

```html
@section scripts {
    <!-- Reuse shared grid component -->
    <script src="~/FrontEnd/Pages/Shared/components/GridComponents.js"></script>

    <!-- Reuse form components -->
    <script src="~/FrontEnd/Pages/Shared/components/FormComponents.js"></script>

    <!-- Bank-specific script -->
    <script src="~/FrontEnd/Pages/Banks/BankList.cshtml.js"></script>
}
```

## Static File Configuration in Startup.cs

```
  app.UseStaticFiles(new StaticFileOptions
  {
      FileProvider = new PhysicalFileProvider(Path.Combine(Directory.GetCurrentDirectory(),
  "FrontEnd")),
      RequestPath = "/FrontEnd"
  });
```