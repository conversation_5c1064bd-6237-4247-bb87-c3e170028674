## 📁 Recommended Directory Structure

```
src/Presentation/ASPNET/FrontEnd/Pages/
├── CustomerContractEntries/
│   ├── CustomerContractEntryList.cshtml
│   ├── CustomerContractEntryList.cshtml.js
│   └── components/
│       ├── ContractTab.js
│       ├── CollectionTab.js
│       ├── CustomerTab.js
│       ├── ProductGrid.js
│       └── CommentGrid.js
├── Shared/
│   └── components/
│       ├── FormComponents.js
│       ├── GridComponents.js
│       └── ModalComponents.js
└── Banks/
    ├── BankList.cshtml
    ├── BankList.cshtml.js
    └── components/
        └── BankGrid.js
```

## 📦 Reference Path Examples

### CustomerContractEntryList.cshtml

```html
@section scripts {
    <!-- Page-specific components -->
    <script src="~/FrontEnd/Pages/CustomerContractEntries/components/ContractTab.js"></script>
    <script src="~/FrontEnd/Pages/CustomerContractEntries/components/ProductGrid.js"></script>

    <!-- Shared components -->
    <script src="~/FrontEnd/Pages/Shared/components/FormComponents.js"></script>

    <!-- Main page script -->
    <script src="~/FrontEnd/Pages/CustomerContractEntries/CustomerContractEntryList.cshtml.js"></script>
}
```

### BankList.cshtml

```html
@section scripts {
    <!-- Reuse shared grid component -->
    <script src="~/FrontEnd/Pages/Shared/components/GridComponents.js"></script>

    <!-- Reuse form components -->
    <script src="~/FrontEnd/Pages/Shared/components/FormComponents.js"></script>

    <!-- Bank-specific script -->
    <script src="~/FrontEnd/Pages/Banks/BankList.cshtml.js"></script>
}
```

## 📚 Component Hierarchy

```
CustomerContractEntries/
├── components/
│   ├── ContractTab.js          # Only used by CustomerContract
│   ├── ProductGrid.js          # Only used by CustomerContract
│   └── CommentGrid.js          # Only used by CustomerContract
└── CustomerContractEntryList.cshtml.js
```

### Level 2: Feature-Shared Components

```
Shared/
└── components/
    ├── forms/
    │   ├── SyncInput.js
    │   ├── SyncDropdown.js
    │   └── SyncDatePicker.js
    ├── grids/
    │   ├── BaseGrid.js
    │   └── EditableGrid.js
    └── modals/
        ├── ConfirmModal.js
        └── LoadingModal.js
```

### Level 3: Cross-Application Components

```
wwwroot/js/shared/
├── utils/
│   ├── ValidationUtils.js
│   └── FormatUtils.js
└── mixins/
    └── ValidationMixin.js
```

## 💡 Benefits

### ✅ Co-location Principle

Files are grouped by page or feature – making them easier to manage and maintain.

### ✅ Clear Ownership

- `CustomerContractEntries/components/` → Managed by CustomerContract team  
- `Shared/components/` → Managed by Platform or UI team  
- Cross-app utilities go under `wwwroot/js/shared/`

### ✅ Reusability Tiers

- **Page-Level**: Quick iteration, no impact on others  
- **App-Level Shared**: Reusable across pages within same app  
- **Cross-App**: Utilities shared across multiple apps

## 📈 Migration Path

1. **Start with Page-Specific:**

```bash
CustomerContractEntries/components/ProductGrid.js
```

2. **When Other Pages Need It → Move to Shared:**

```bash
mv CustomerContractEntries/components/ProductGrid.js Shared/components/grids/
```

3. **Update Reference:**

```html
<!-- Old -->
<script src="~/FrontEnd/Pages/CustomerContractEntries/components/ProductGrid.js"></script>

<!-- New -->
<script src="~/FrontEnd/Pages/Shared/components/grids/ProductGrid.js"></script>
```

## 🔧 Implementation Example

### Component: `ContractTab.js`

```js
// src/Presentation/ASPNET/FrontEnd/Pages/CustomerContractEntries/components/ContractTab.js
var ContractTab = {
    props: {
        contractData: Object,
        errors: Object
    },
    template: `
        <div class="responsive-align">
            <div class="card-body bg-body-tertiary">
                <label>Contract Details</label>
                <div class="row mb-3">
                    <sync-input 
                        v-model="contractData.contractNo"
                        label="Contract No"
                        :error="errors.contractNo"
                        required>
                    </sync-input>
                </div>
            </div>
        </div>
    `,
    setup(props) {
        return {};
    }
};
```

### Usage in `CustomerContractEntryList.cshtml`

```html
@section scripts {
    <!-- Load shared components first -->
    <script src="~/FrontEnd/Pages/Shared/components/FormComponents.js"></script>

    <!-- Load page components -->
    <script src="~/FrontEnd/Pages/CustomerContractEntries/components/ContractTab.js"></script>

    <!-- Load main script last -->
    <script src="~/FrontEnd/Pages/CustomerContractEntries/CustomerContractEntryList.cshtml.js"></script>
}
```
