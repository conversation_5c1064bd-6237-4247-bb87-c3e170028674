const App = {
    setup() {
        const state = Vue.reactive({
            mainData: [],
            deleteMode: false,
            mainTitle: null,
            id: '',
            cancellationPeriod: '',
            collectionRange: '',
            collectionRate: '',
            chargebackRate: '',
            chargebackInterval: '',
            salesCommissionChargebackRateCode: '',
            isActive: true,

            errors: {
                cancellationPeriod: '',
                collectionRate: '',
                chargebackRate: '',
                chargebackInterval: '',
                salesCommissionChargebackRateCode: '',
                isActive: '',
            },
            isSubmitting: false,
        });

        const mainGridRef = Vue.ref(null);
        const mainModalRef = Vue.ref(null);
        const cancellationPeriodRef = Vue.ref(null);
        const collectionRangeRef = Vue.ref(null);
        const collectionRateRef = Vue.ref(null);
        const chargebackRateRef = Vue.ref(null);
        const chargebackIntervalRef = Vue.ref(null);
        const salesCommissionChargebackRateCodeRef = null;
        const isActiveRef = Vue.ref(null);

        const services = {
            getMainData: async () => {
                try {
                    const response = await AxiosManager.get('/SalesCommissionChargeBackRate/GetSalesCommissionChargeBackRateList', {});
                    return response;
                } catch (error) {
                    throw error;
                }
            },
            createMainData: async (cancellationPeriod, collectionRange, collectionRate, chargebackInterval, chargebackRate, salesCommissionChargebackRateCode, isActive, createdById) => {
                try {
                    const response = await AxiosManager.post('/SalesCommissionChargeBackRate/CreateSalesCommissionChargeBackRate', {
                        cancellationPeriod, collectionRange, collectionRate, chargebackInterval, chargebackRate, salesCommissionChargebackRateCode, isActive, createdById
                    });
                    return response;
                } catch (error) {
                    throw error;
                }
            },
            updateMainData: async (id, cancellationPeriod, collectionRange, collectionRate, chargebackInterval, chargebackRate, salesCommissionChargebackRateCode, isActive, updatedById) => {
                try {
                    const response = await AxiosManager.post('/SalesCommissionChargeBackRate/UpdateSalesCommissionChargeBackRate', {
                        id, cancellationPeriod, collectionRange, collectionRate, chargebackInterval, chargebackRate, salesCommissionChargebackRateCode, isActive, updatedById
                    });
                    return response;
                } catch (error) {
                    throw error;
                }
            },
            deleteMainData: async (id, deletedById) => {
                try {
                    const response = await AxiosManager.post('/SalesCommissionChargeBackRate/DeleteSalesCommissionChargeBackRate', {
                        id, deletedById
                    });
                    return response;
                } catch (error) {
                    throw error;
                }
            },
        };

        const methods = {
            populateMainData: async () => {
                const response = await services.getMainData();
                const formattedData = response?.data?.content?.data.map(item => ({
                    ...item,
                    createdAtUtc: new Date(item.createdAtUtc)
                }));
                state.mainData = formattedData;
            },
            onMainModalHidden: () => {
                state.errors.cancellationPeriod = '';
                state.errors.collectionRate = '';
                state.errors.collectionRange = '';
                state.errors.chargebackRate = '';
                state.errors.chargebackInterval = '';
                state.errors.isActive = '';

                setTimeout(() => {
                    cancellationPeriodDropdown.refresh();
                    collectionRangeDropdown.refresh();
                    collectionRateText.refresh();
                    chargebackRateText.refresh();
                    chargebackIntervalDropdown.refresh();
                }, 100);

            },

            fillFormFromRecord: (record) => {
                const keys = ['id', 'cancellationPeriod', 'collectionRange', 'collectionRate', 'chargebackRate', 'chargebackInterval', 'salesCommissionChargebackRateCode', 'isActive'];
                keys.forEach(k => state[k] = record?.[k] ?? '');
            },
           
        };

        const mainGrid = {
            obj: null,
            create: async (dataSource) => {
                mainGrid.obj = new ej.grids.Grid({
                    height: getDashminGridHeight(),
                    dataSource: dataSource,
                    allowFiltering: true,
                    allowSorting: true,
                    allowSelection: true,
                    allowGrouping: true,
                    allowTextWrap: true,
                    allowResizing: true,
                    allowPaging: true,
                    allowExcelExport: true,
                    filterSettings: { type: 'CheckBox' },
                    sortSettings: { columns: [{ field: 'cancellationPeriod', direction: 'Descending' }] },
                    pageSettings: { currentPage: 1, pageSize: 50, pageSizes: ["10", "20", "50", "100", "200", "All"] },
                    selectionSettings: { persistSelection: true, type: 'Single' },
                    autoFit: true,
                    showColumnMenu: true,
                    gridLines: 'Horizontal',
                    columns: [
                        { type: 'checkbox', width: 60 },
                        {
                            field: 'id', isPrimaryKey: true, headerText: 'Id', visible: false
                        },
                        { field: 'salesCommissionChargebackRateCode', headerText: 'Sales Commission Chargeback Rate Code', visible: false },
                        { field: 'cancellationPeriod', headerText: 'Cancellation Period', width: 200, minWidth: 200 },
                        { field: 'collectionRange', headerText: 'Range', width: 100, minWidth: 100 },
                        { field: 'collectionRate', headerText: 'Collection Percentage (%)', width: 150, minWidth: 150 },
                        {
                            field: 'chargebackRate',
                            headerText: 'Chargeback Percentage (%)',
                            width: 150,
                            minWidth: 150,
                            format: '###.00',
                            valueFormatter: (args) => {
                                return args.value ? args.value.toFixed(2) + '%' : '';
                            }
                        },
                        { field: 'chargebackInterval', headerText: 'Chargeback Interval', width: 200, minWidth: 200 },
                        { field: 'isActive',
                            headerText: 'Status',
                            width: 50,
                            minWidth: 50,
                            valueAccessor: function(field, data, column) {
                                return data.isActive ? 'Yes' : 'No';
                            }
                        },
                        //{ field: 'createdAtUtc', headerText: 'Created At UTC', width: 150, format: 'yyyy-MM-dd HH:mm' }
                    ],
                    toolbar: [
                        'ExcelExport', 'Search',
                        { type: 'Separator' },
                        { text: 'Add', tooltipText: 'Add', prefixIcon: 'e-add', id: 'AddCustom' },
                        { text: 'Edit', tooltipText: 'Edit', prefixIcon: 'e-edit', id: 'EditCustom' },
                        { text: 'Delete', tooltipText: 'Delete', prefixIcon: 'e-delete', id: 'DeleteCustom' },
                        { type: 'Separator' },
                    ],
                    beforeDataBound: () => { },
                    dataBound: function () {
                        mainGrid.obj.toolbarModule.enableItems(['EditCustom', 'DeleteCustom'], false);
                        mainGrid.obj.autoFitColumns(['cancellationPeriod', 'collectionRange', 'collectionRate', 'chargebackRate', 'chargebackInterval', 'salesCommissionChargebackRateCode', 'isActive']);
                    },
                    excelExportComplete: () => { },
                    rowSelected: () => {
                        if (mainGrid.obj.getSelectedRecords().length === 1) {
                            mainGrid.obj.toolbarModule.enableItems(['EditCustom', 'DeleteCustom'], true);
                        } else {
                            mainGrid.obj.toolbarModule.enableItems(['EditCustom', 'DeleteCustom'], false);
                        }
                    },
                    rowDeselected: () => {
                        if (mainGrid.obj.getSelectedRecords().length === 1) {
                            mainGrid.obj.toolbarModule.enableItems(['EditCustom', 'DeleteCustom'], true);
                        } else {
                            mainGrid.obj.toolbarModule.enableItems(['EditCustom', 'DeleteCustom'], false);
                        }
                    },
                    rowSelecting: () => {
                        if (mainGrid.obj.getSelectedRecords().length) {
                            mainGrid.obj.clearSelection();
                        }
                    },
                    toolbarClick: (args) => {
                        if (args.item.id === 'MainGrid_excelexport') {
                            mainGrid.obj.excelExport();
                        }

                        if (args.item.id === 'AddCustom') {
                            state.deleteMode = false;
                            state.mainTitle = 'Add Sales Commission Charge Back Rate';
                            state.isActive = true;
                            methods.fillFormFromRecord({
                                id: '',
                                cancellationPeriod: '',
                                collectionRange: '',
                                collectionRate: '0',
                                chargebackRate: '0',
                                chargebackInterval: '',
                                salesCommissionChargebackRateCode: '',
                                isActive: true
                            });
                            setTimeout(() => {
                                if (cancellationPeriodDropdown.obj) {
                                    cancellationPeriodDropdown.obj.enabled = true;
                                    cancellationPeriodDropdown.obj.value = null;
                                    cancellationPeriodDropdown.obj.dataBind();
                                }
                                if (collectionRangeDropdown.obj) {
                                    collectionRangeDropdown.obj.enabled = true;
                                    collectionRangeDropdown.obj.value = null;
                                    collectionRangeDropdown.obj.dataBind();
                                }
                                if (chargebackIntervalDropdown.obj) {
                                    chargebackIntervalDropdown.obj.enabled = true;
                                    chargebackIntervalDropdown.obj.value = null;
                                    chargebackIntervalDropdown.obj.dataBind();
                                }
                                collectionRateText.refresh();
                                chargebackRateText.refresh();
                            }, 100);
                            mainModal.obj.show();
                        }

                        if (args.item.id === 'EditCustom') {
                            state.deleteMode = false;
                            if (mainGrid.obj.getSelectedRecords().length) {
                                const selectedRecord = mainGrid.obj.getSelectedRecords()[0];
                                state.mainTitle = 'Edit Sales Commission Charge Back Rate';
                                methods.fillFormFromRecord(selectedRecord);
                                setTimeout(() => {
                                    cancellationPeriodDropdown.refresh();
                                    collectionRangeDropdown.refresh();
                                    collectionRateText.refresh();
                                    chargebackRateText.refresh();
                                    chargebackIntervalDropdown.refresh();
                                }, 100);
                                mainModal.obj.show();
                            }
                        }

                        if (args.item.id === 'DeleteCustom') {
                            state.deleteMode = true;
                            if (mainGrid.obj.getSelectedRecords().length) {
                                const selectedRecord = mainGrid.obj.getSelectedRecords()[0];
                                state.mainTitle = 'Delete Sales Commission Charge Back Rate?';
                                methods.fillFormFromRecord(selectedRecord);
                                mainModal.obj.show();
                            }
                        }
                    }
                });

                mainGrid.obj.appendTo(mainGridRef.value);
            },
            refresh: () => {
                mainGrid.obj.setProperties({ dataSource: state.mainData });
            }
        };

        const mainModal = {
            obj: null,
            create: () => {
                mainModal.obj = new bootstrap.Modal(mainModalRef.value, {
                    backdrop: 'static',
                    keyboard: false
                });
            }
        };

        /** 
        const cancellationPeriodText = {
            obj: null,
            create: () => {
                cancellationPeriodText.obj = new ej.inputs.TextBox({
                    placeholder: 'Enter Cancellation Period',
                    format: '###.00',
                    step: 0.1,
                    input: (args) => {
                        if (cancellationPeriodText.obj.enabled) {
                            const upperCaseValue = args.value.toUpperCase();
                            cancellationPeriodText.obj.value = upperCaseValue;
                            state.cancellationPeriod = upperCaseValue;
                        }
                    }
                });
                cancellationPeriodText.obj.appendTo(cancellationPeriodRef.value);
            },
            refresh: () => {
                if (cancellationPeriodText.obj) {
                    cancellationPeriodText.obj.value = state.cancellationPeriod;
                    cancellationPeriodText.obj.dataBind();
                    cancellationPeriodText.obj.enabled = !(state.mainTitle && state.mainTitle.includes('Edit'));
                }
            }
        };
        */

        const cancellationPeriodDropdown = {
            obj: null,
            cancellationPeriods: ['CANCELLATION', 'CANCELLATION-CHARGE OFF', 'DPO3','PP4'],
            create: () => {
                cancellationPeriodDropdown.obj = new ej.dropdowns.DropDownList({
                    dataSource: cancellationPeriodDropdown.cancellationPeriods,
                    placeholder: 'Select Cancellation Period',
                    value: state.cancellationPeriod,
                    enabled: state.mainTitle !== 'View Sales Commission Charge Back Rate' && state.mainTitle !== 'Edit Sales Commission Charge Back Rate',
                    change: (args) => {
                        state.cancellationPeriod = args.value;
                    }
                });
                cancellationPeriodDropdown.obj.appendTo(cancellationPeriodRef.value);
            },
            refresh: () => {
                if (cancellationPeriodDropdown.obj) {
                    cancellationPeriodDropdown.obj.value = state.cancellationPeriod;
                    cancellationPeriodDropdown.obj.dataBind();
                    cancellationPeriodDropdown.obj.enabled = state.mainTitle !== 'View Sales Commission Charge Back Rate' && state.mainTitle !== 'Edit Sales Commission Charge Back Rate';
                }
            }
        };

        const collectionRangeDropdown = {
            obj: null,
            collectionRanges: ['>', '<', '>=', '<=', '='],
            create: () => {
                collectionRangeDropdown.obj = new ej.dropdowns.DropDownList({
                    dataSource: collectionRangeDropdown.collectionRanges,
                    placeholder: 'Select Collection Range',
                    value: state.collectionRange,
                    enabled: state.mainTitle !== 'View Sales Commission Charge Back Rate' && state.mainTitle !== 'Edit Sales Commission Charge Back Rate',
                    change: (args) => {
                        state.collectionRange = args.value;
                    }
                });
                collectionRangeDropdown.obj.appendTo(collectionRangeRef.value);
            },
            refresh: () => {
                if (collectionRangeDropdown.obj) {
                    collectionRangeDropdown.obj.value = state.collectionRange;
                    collectionRangeDropdown.obj.dataBind();
                    collectionRangeDropdown.obj.enabled = state.mainTitle !== 'View Sales Commission Charge Back Rate' && state.mainTitle !== 'Edit Sales Commission Charge Back Rate';
                }
            }
        };

        const collectionRateText = {
            obj: null,
            create: () => {
                collectionRateText.obj = new ej.inputs.NumericTextBox({
                    placeholder: 'Enter Collection Rate %',
                    format: '###.00',
                    step: 0.1,
                    change: (args) => {
                        state.collectionRate = args.value;
                    }
                });
                collectionRateText.obj.appendTo(collectionRateRef.value);
            },
            refresh: () => {
                if (collectionRateText.obj) {
                    collectionRateText.obj.value = parseFloat(state.collectionRate || 0);
                    collectionRateText.obj.dataBind();
                    collectionRateText.obj.enabled = !(state.mainTitle && state.mainTitle.includes('Edit'));
                }
            }
        };

        

        const chargebackRateText = {
            obj: null,
            create: () => {
                chargebackRateText.obj = new ej.inputs.NumericTextBox({
                    placeholder: 'Enter Chargeback Rate %',
                    format: '###.00',
                    step: 0.1,
                    change: (args) => {
                        state.chargebackRate = args.value;
                    }
                });
                chargebackRateText.obj.appendTo(chargebackRateRef.value);
            },
            refresh: () => {
                if (chargebackRateText.obj) {
                    chargebackRateText.obj.value = parseFloat(state.chargebackRate || 0);
                    chargebackRateText.obj.dataBind();
                }
            }
        };

        const chargebackIntervalDropdown = {
            obj: null,
            chargebackIntervals: ['WEEKLY', 'MONTHLY'],
            create: () => {
                chargebackIntervalDropdown.obj = new ej.dropdowns.DropDownList({
                    dataSource: chargebackIntervalDropdown.chargebackIntervals,
                    placeholder: 'Select Chargeback Interval',
                    value: state.chargebackInterval,
                    enabled: state.mainTitle !== 'View Sales Commission Charge Back Rate' ,
                    change: (args) => {
                        state.chargebackInterval = args.value;
                    }
                });
                chargebackIntervalDropdown.obj.appendTo(chargebackIntervalRef.value);
            },
            refresh: () => {
                if (chargebackIntervalDropdown.obj) {
                    chargebackIntervalDropdown.obj.value = state.chargebackInterval;
                    chargebackIntervalDropdown.obj.dataBind();
                    chargebackIntervalDropdown.obj.enabled = state.mainTitle !== 'View Sales Commission Charge Back Rate';
                }
            }
        };



        const validateForm = function () {
            state.errors.cancellationPeriod = '';
            state.errors.collectionRate = '';
            state.errors.chargebackRate = '';
            state.errors.collectionRange = '';
            state.errors.chargebackInterval = '';
            state.errors.isActive = '';

            let isValid = true;

            if (!state.cancellationPeriod) {
                state.errors.cancellationPeriod = 'Cancellation Period is required.';
                isValid = false;
            }

            if (!state.collectionRange) {
                state.errors.collectionRange = 'Collection Range is required.';
                isValid = false;
            }

            if (state.collectionRate === '' || state.collectionRate === null || state.collectionRate === undefined) {
                state.errors.collectionRate = 'Collection Rate is required.';
                isValid = false;
            } else if (state.collectionRate < 0) {
                state.errors.collectionRate = 'Collection Rate must be a non-negative number.';
                isValid = false;
            }

            if (state.chargebackRate === '' || state.chargebackRate === null || state.chargebackRate === undefined) {
                state.errors.chargebackRate = 'Chargeback Rate is required.';
                isValid = false;
            } else if (state.chargebackRate < 0) {
                state.errors.chargebackRate = 'Chargeback Rate must be a non-negative number.';
                isValid = false;
            }

            if (!state.chargebackInterval) {
                state.errors.chargebackInterval = 'Chargeback Interval is required.';
                isValid = false;
            }

            return isValid;
        };

        const handler = {
            handleSubmit: async function () {
                try {
                    state.isSubmitting = true;
                    await new Promise(resolve => setTimeout(resolve, 300));

                    if (!validateForm()) {
                        return;
                    }
                    state.salesCommissionChargebackRateCode = `${state.cancellationPeriod}${state.collectionRange}${state.collectionRate}`;
                    const isActive = state.isActive;

                    const response = state.id === ''
                            ? await services.createMainData(state.cancellationPeriod, state.collectionRange, state.collectionRate, state.chargebackInterval, state.chargebackRate, state.salesCommissionChargebackRateCode, isActive, StorageManager.getUserId())
                        : state.deleteMode
                            ? await services.deleteMainData(state.id, StorageManager.getUserId())
                            : await services.updateMainData(state.id, state.cancellationPeriod, state.collectionRange, state.collectionRate, state.chargebackInterval, state.chargebackRate, state.salesCommissionChargebackRateCode, isActive, StorageManager.getUserId());

                    if (response.data.code === 200) {
                        await methods.populateMainData();
                        mainGrid.refresh();
                        Swal.fire({
                            icon: 'success',
                            title: state.deleteMode ? 'Delete Successful' : 'Save Successful',
                            text: 'Form will be closed...',
                            timer: 2000,
                            showConfirmButton: false
                        });
                        setTimeout(() => {
                            mainModal.obj.hide();
                        }, 2000);
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: state.deleteMode ? 'Delete Failed' : 'Save Failed',
                            text: response.data.message ?? 'Please check your data.',
                            confirmButtonText: 'Try Again'
                        });
                    }

                } catch (error) {
                    Swal.fire({
                        icon: 'error',
                        title: 'An Error Occurred',
                        text: error.response?.data?.message ?? 'Please try again.',
                        confirmButtonText: 'OK'
                    });
                } finally {
                    state.isSubmitting = false;
                }
            },
        };

        Vue.watch(
            () => state.cancellationPeriod,
            (newVal, oldVal) => {
                state.errors.cancellationPeriod = '';
                cancellationPeriodDropdown.refresh();
                chargebackIntervalDropdown.refresh();

            }
        );

        Vue.watch(
            () => state.collectionRange,
            (newVal, oldVal) => {
                state.errors.collectionRange = '';
            }
        );

        Vue.watch(
            () => state.collectionRate,
            (newVal, oldVal) => {
                state.errors.collectionRate = '';
                collectionRateText.refresh();
            }
        );

        Vue.watch(
            () => state.chargebackRate,
            (newVal, oldVal) => {
                state.errors.chargebackRate = '';
                chargebackRateText.refresh();
            }
        );

        Vue.watch(
            () => state.chargebackInterval,
            (newVal, oldVal) => {
                state.errors.chargebackInterval = '';
            }
        );

        Vue.watch(
            () => state.salesCommissionChargebackRateCode,
            (newVal, oldVal) => {
                state.errors.salesCommissionChargebackRateCode = '';
            }
        );



        Vue.onMounted(async () => {
            try {
                //await SecurityManager.authorizePage(['SalesCommissionChargeBackRate']);
                await SecurityManager.validateToken();
                await methods.populateMainData();
                await mainGrid.create(state.mainData);
                cancellationPeriodDropdown.create();
                collectionRangeDropdown.create();
                collectionRateText.create();
                chargebackRateText.create();
                chargebackIntervalDropdown.create();
                mainModal.create();
                mainModalRef.value?.addEventListener('hidden.bs.modal', methods.onMainModalHidden);
            } catch (e) {
                console.error('page init error:', e);
            } finally {
                hideSpinnerAndShowContent();
            }
        });

        Vue.onUnmounted(() => {
            mainModalRef.value?.removeEventListener('hidden.bs.modal', methods.onMainModalHidden);
        });

        return {
            mainGridRef,
            mainModalRef,
            cancellationPeriodRef,
            collectionRangeRef,
            collectionRateRef,
            chargebackRateRef,
            chargebackIntervalRef,
            isActiveRef,
            state,
            handler,
        };
    }


};

Vue.createApp(App).mount('#app');