﻿@page
@{
    ViewData["Title"] = "Sales Commission Chargeback Rate List";
}

<div id="app" v-cloak>
    <div class="row">
        <div class="col-12">
            <div class="grid-container">
                <div ref="mainGridRef"></div>
            </div>
        </div>
    </div>

    <div class="modal fade" ref="mainModalRef" id="MainModal" tabindex="-1" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ state.mainTitle }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body bg-body-tertiary">
                    <form id="MainForm">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>Sales Commission Chargeback Rate Info</h5>
                                    </div>
                                    <div class="card-body bg-body-tertiary">
                                        <div class="row mb-3">
                                            <div class="col-md-4">
                                                <label for="Cancellation Period">Cancellation Period</label>
                                                <input ref="cancellationPeriodRef" type="text" v-model="state.cancellationPeriod">
                                                <label class="text-danger">{{ state.errors.cancellationPeriod }}</label>
                                            </div>
                                            <div class="col-md-4">
                                                <label for="Collection Range">Collection Range</label>
                                                <input ref="collectionRangeRef" type="text" v-model="state.collectionRange">
                                                <label class="text-danger">{{ state.errors.collectionRange }}</label>
                                            </div>
                                            <div class="col-md-4">
                                                <label for="Collection Rate">Collection Percentage (%) </label>
                                                <input ref="collectionRateRef" type="text" v-model="state.collectionRate">
                                                <label class="text-danger">{{ state.errors.collectionRate }}</label>
                                            </div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-md-4">
                                                <label for="Chargeback Rate">Chargeback Percentage (%) </label>
                                                <input ref="chargebackRateRef" type="text" v-model="state.chargebackRate">
                                                <label class="text-danger">{{ state.errors.chargebackRate }}</label>
                                            </div>
                                            <div class="col-md-4">
                                                <label for="Chargeback Interval">Chargeback Interval</label>
                                                <input ref="chargebackIntervalRef" type="text" v-model="state.chargebackInterval">
                                                <label class="text-danger">{{ state.errors.chargebackInterval }}</label>
                                            </div>
                                            <div class="col-md-4">
                                                <label for="IsActive" class="me-3">Active</label>
                                                <input type="checkbox" ref="isActiveRef" v-model="state.isActive" style="width: 20px; height: 20px;">
                                                <label class="text-danger">{{ state.errors.isActive }}</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button"
                            id="MainSaveButton"
                            class="btn"
                            v-bind:class="state.deleteMode ? 'btn-danger' : 'btn-primary'"
                            v-on:click="handler.handleSubmit"
                            v-bind:disabled="state.isSubmitting">
                        <span class="spinner-border spinner-border-sm me-2" v-if="state.isSubmitting" role="status" aria-hidden="true"></span>
                        <span v-if="!state.isSubmitting">{{ state.deleteMode ? 'Delete' : 'Save' }}</span>
                        <span v-else>{{ state.deleteMode ? 'Deleting...' : 'Saving...' }}</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@section scripts {
    <script src="~/FrontEnd/Pages/SalesCommissionChargeBackRate/SalesCommissionChargeBackRateList.cshtml.js"></script>
}