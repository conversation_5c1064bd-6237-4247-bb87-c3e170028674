@page
@{
    ViewData["Title"] = "Customer Contract Entry - Component Demo";
}

<!-- Component Demo showing Strategy 4 implementation -->
<div id="contractApp">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <h4 class="mb-4">Customer Contract Entry - Using Shared Components</h4>
                
                <!-- Customer Information Form -->
                <customer-info-form 
                    v-bind:form-data="contractForm" 
                    v-bind:customers="customerList"
                    v-on:customer-selected="onCustomerSelected">
                </customer-info-form>

                <!-- Contract Details Form -->
                <contract-details-form 
                    v-bind:form-data="contractForm"
                    v-bind:payment-types="paymentTypes">
                </contract-details-form>

                <!-- Sales Information Form -->
                <sales-info-form 
                    v-bind:form-data="contractForm"
                    v-bind:salesmen="salesmenList"
                    v-bind:regions="regionList"
                    v-bind:locations="locationList"
                    v-on:salesman-selected="onSalesmanSelected">
                </sales-info-form>

                <!-- Action Buttons -->
                <div class="card">
                    <div class="card-body">
                        <button type="button" class="btn btn-primary me-2" v-on:click="saveContract">
                            Save Contract
                        </button>
                        <button type="button" class="btn btn-secondary" v-on:click="resetForm">
                            Reset
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <!-- Load shared components first -->
    <script src="~/FrontEnd/Components/Shared/FormComponents.js"></script>
    
    <!-- Load feature-specific components -->
    <script src="~/FrontEnd/Components/CustomerContractEntry/ContractFormComponents.js"></script>
    
    <!-- Load component loader utility -->
    <script src="~/FrontEnd/Pages/Shared/ComponentLoader.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize the Vue app with shared and feature components
            const app = ComponentLoader.initializeFeatureApp('Contract', {
                data() {
                    return {
                        contractForm: {
                            // Customer Info
                            customerId: '',
                            customerName: '',
                            address: '',
                            
                            // Contract Details
                            contractNo: '',
                            contractDate: new Date().toISOString().split('T')[0],
                            deliveryDate: new Date().toISOString().split('T')[0],
                            commenceDate: '',
                            paymentType: '',
                            term: '',
                            
                            // Sales Info
                            salesmanId: '',
                            divisionManager: '',
                            region: '',
                            location: '',
                            fairCode: ''
                        },
                        
                        // Mock data - in real app, these would come from APIs
                        customerList: [
                            { id: 'C001', name: 'ABC Corporation', address: '123 Business St, City' },
                            { id: 'C002', name: 'XYZ Limited', address: '456 Commerce Ave, Town' }
                        ],
                        
                        paymentTypes: [
                            { id: 'CASH', name: 'Cash' },
                            { id: 'CREDIT', name: 'Credit' },
                            { id: 'INSTALLMENT', name: 'Installment' }
                        ],
                        
                        salesmenList: [
                            { id: 'S001', name: 'John Smith', divisionManager: 'Jane Doe' },
                            { id: 'S002', name: 'Mike Johnson', divisionManager: 'Bob Wilson' }
                        ],
                        
                        regionList: [
                            { code: 'NORTH', name: 'Northern Region' },
                            { code: 'SOUTH', name: 'Southern Region' }
                        ],
                        
                        locationList: [
                            { code: 'KL', name: 'Kuala Lumpur' },
                            { code: 'PG', name: 'Penang' }
                        ]
                    };
                },
                
                watch: {
                    'contractForm.deliveryDate'(newDate) {
                        if (newDate) {
                            // Auto-calculate commence date (45 days after delivery)
                            const deliveryDate = new Date(newDate);
                            deliveryDate.setDate(deliveryDate.getDate() + 45);
                            this.contractForm.commenceDate = deliveryDate.toISOString().split('T')[0];
                        }
                    }
                },
                
                methods: {
                    onCustomerSelected(customer) {
                        this.contractForm.customerName = customer.name;
                        this.contractForm.address = customer.address;
                        console.log('Customer selected:', customer);
                    },
                    
                    onSalesmanSelected(salesman) {
                        this.contractForm.divisionManager = salesman.divisionManager;
                        console.log('Salesman selected:', salesman);
                    },
                    
                    saveContract() {
                        console.log('Saving contract:', this.contractForm);
                        // Here you would call your API endpoint
                        alert('Contract saved successfully! (Demo)');
                    },
                    
                    resetForm() {
                        // Reset form to defaults
                        Object.keys(this.contractForm).forEach(key => {
                            if (key === 'contractDate' || key === 'deliveryDate') {
                                this.contractForm[key] = new Date().toISOString().split('T')[0];
                            } else {
                                this.contractForm[key] = '';
                            }
                        });
                    }
                }
            }, '#contractApp');

            if (app) {
                console.log('Customer Contract Entry app initialized successfully');
            }
        });
    </script>
}