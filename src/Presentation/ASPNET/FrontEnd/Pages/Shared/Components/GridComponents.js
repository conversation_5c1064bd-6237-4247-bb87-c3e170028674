/**
 * GridComponents.js
 * Reusable grid components and utilities for Syncfusion Grid
 * Provides common grid configurations and helper functions
 */

/**
 * BaseGrid - Base grid component with common configurations
 */
const BaseGrid = {
    name: 'BaseGrid',
    props: {
        dataSource: {
            type: Array,
            default: () => []
        },
        columns: {
            type: Array,
            required: true
        },
        height: {
            type: [String, Number],
            default: () => getDashminGridHeight ? getDashminGridHeight() : 400
        },
        allowFiltering: {
            type: Boolean,
            default: true
        },
        allowSorting: {
            type: Boolean,
            default: true
        },
        allowPaging: {
            type: Boolean,
            default: true
        },
        allowSelection: {
            type: Boolean,
            default: true
        },
        allowGrouping: {
            type: Boolean,
            default: true
        },
        allowExcelExport: {
            type: Boolean,
            default: true
        },
        allowResizing: {
            type: Boolean,
            default: true
        },
        allowTextWrap: {
            type: Boolean,
            default: true
        },
        pageSize: {
            type: Number,
            default: 50
        },
        toolbar: {
            type: Array,
            default: () => ['ExcelExport', 'Search']
        },
        selectionType: {
            type: String,
            default: 'Single',
            validator: value => ['Single', 'Multiple'].includes(value)
        },
        gridLines: {
            type: String,
            default: 'Horizontal',
            validator: value => ['None', 'Horizontal', 'Vertical', 'Both'].includes(value)
        }
    },
    emits: ['toolbar-click', 'row-selected', 'row-deselected', 'data-bound'],
    setup(props, { emit, expose }) {
        const gridRef = Vue.ref(null);
        let gridObj = null;

        const createGrid = () => {
            if (!gridRef.value) return;

            const gridConfig = {
                height: props.height,
                dataSource: props.dataSource,
                allowFiltering: props.allowFiltering,
                allowSorting: props.allowSorting,
                allowSelection: props.allowSelection,
                allowGrouping: props.allowGrouping,
                allowTextWrap: props.allowTextWrap,
                allowResizing: props.allowResizing,
                allowPaging: props.allowPaging,
                allowExcelExport: props.allowExcelExport,
                filterSettings: { type: 'CheckBox' },
                pageSettings: { 
                    currentPage: 1, 
                    pageSize: props.pageSize, 
                    pageSizes: ["10", "20", "50", "100", "200", "All"] 
                },
                selectionSettings: { 
                    persistSelection: true, 
                    type: props.selectionType 
                },
                autoFit: true,
                showColumnMenu: true,
                gridLines: props.gridLines,
                columns: props.columns,
                toolbar: props.toolbar,
                beforeDataBound: () => { },
                dataBound: function () {
                    emit('data-bound');
                },
                excelExportComplete: () => { },
                rowSelected: (args) => {
                    emit('row-selected', args);
                },
                rowDeselected: (args) => {
                    emit('row-deselected', args);
                },
                toolbarClick: (args) => {
                    if (args.item.id.includes('excelexport')) {
                        gridObj.excelExport();
                    } else {
                        emit('toolbar-click', args);
                    }
                }
            };

            gridObj = new ej.grids.Grid(gridConfig);
            gridObj.appendTo(gridRef.value);
        };

        const refreshGrid = () => {
            if (gridObj) {
                gridObj.setProperties({ dataSource: props.dataSource });
            }
        };

        const getSelectedRecords = () => {
            return gridObj ? gridObj.getSelectedRecords() : [];
        };

        const clearSelection = () => {
            if (gridObj) {
                gridObj.clearSelection();
            }
        };

        const enableToolbarItems = (items, enable = true) => {
            if (gridObj && gridObj.toolbarModule) {
                gridObj.toolbarModule.enableItems(items, enable);
            }
        };

        const exportToExcel = (fileName = 'GridData') => {
            if (gridObj) {
                gridObj.excelExport({ fileName });
            }
        };

        const search = (searchText) => {
            if (gridObj) {
                gridObj.search(searchText);
            }
        };

        const clearSearch = () => {
            if (gridObj) {
                gridObj.search('');
            }
        };

        // Watch for data source changes
        Vue.watch(
            () => props.dataSource,
            () => {
                refreshGrid();
            },
            { deep: true }
        );

        Vue.onMounted(() => {
            createGrid();
        });

        Vue.onUnmounted(() => {
            if (gridObj) {
                gridObj.destroy();
            }
        });

        // Expose methods for parent component
        expose({
            refreshGrid,
            getSelectedRecords,
            clearSelection,
            enableToolbarItems,
            exportToExcel,
            search,
            clearSearch,
            gridInstance: () => gridObj
        });

        return {
            gridRef
        };
    },
    template: `
        <div class="grid-container">
            <div ref="gridRef"></div>
        </div>
    `
};

/**
 * GridUtils - Utility functions for grid operations
 */
const GridUtils = {
    /**
     * Create standard CRUD toolbar
     * @param {Object} options - Toolbar options
     * @returns {Array} Toolbar configuration
     */
    createCrudToolbar: function(options = {}) {
        const {
            showAdd = true,
            showEdit = true,
            showDelete = true,
            showExport = true,
            showSearch = true,
            customButtons = []
        } = options;

        const toolbar = [];

        if (showExport) toolbar.push('ExcelExport');
        if (showSearch) toolbar.push('Search');
        
        if (showAdd || showEdit || showDelete || customButtons.length > 0) {
            toolbar.push({ type: 'Separator' });
        }

        if (showAdd) {
            toolbar.push({ 
                text: 'Add', 
                tooltipText: 'Add', 
                prefixIcon: 'e-add', 
                id: 'AddCustom' 
            });
        }

        if (showEdit) {
            toolbar.push({ 
                text: 'Edit', 
                tooltipText: 'Edit', 
                prefixIcon: 'e-edit', 
                id: 'EditCustom' 
            });
        }

        if (showDelete) {
            toolbar.push({ 
                text: 'Delete', 
                tooltipText: 'Delete', 
                prefixIcon: 'e-delete', 
                id: 'DeleteCustom' 
            });
        }

        // Add custom buttons
        customButtons.forEach(button => {
            toolbar.push(button);
        });

        if (customButtons.length > 0) {
            toolbar.push({ type: 'Separator' });
        }

        return toolbar;
    },

    /**
     * Create standard column configuration
     * @param {Object} columnDef - Column definition
     * @returns {Object} Column configuration
     */
    createColumn: function(columnDef) {
        const {
            field,
            headerText,
            width = null,
            minWidth = null,
            type = null,
            format = null,
            visible = true,
            allowSorting = true,
            allowFiltering = true,
            isPrimaryKey = false,
            template = null
        } = columnDef;

        const column = {
            field,
            headerText,
            visible,
            allowSorting,
            allowFiltering,
            isPrimaryKey
        };

        if (width) column.width = width;
        if (minWidth) column.minWidth = minWidth;
        if (type) column.type = type;
        if (format) column.format = format;
        if (template) column.template = template;

        return column;
    },

    /**
     * Create checkbox column
     * @param {number} width - Column width
     * @returns {Object} Checkbox column configuration
     */
    createCheckboxColumn: function(width = 60) {
        return { type: 'checkbox', width };
    },

    /**
     * Create ID column (hidden primary key)
     * @param {string} field - Field name
     * @returns {Object} ID column configuration
     */
    createIdColumn: function(field = 'id') {
        return {
            field,
            isPrimaryKey: true,
            headerText: 'Id',
            visible: false
        };
    },

    /**
     * Create date column with formatting
     * @param {string} field - Field name
     * @param {string} headerText - Column header text
     * @param {string} format - Date format
     * @param {number} width - Column width
     * @returns {Object} Date column configuration
     */
    createDateColumn: function(field, headerText, format = 'yyyy-MM-dd HH:mm', width = 150) {
        return {
            field,
            headerText,
            width,
            format,
            type: 'date'
        };
    },

    /**
     * Handle standard CRUD toolbar clicks
     * @param {Object} args - Toolbar click arguments
     * @param {Object} grid - Grid instance
     * @param {Object} callbacks - Callback functions
     */
    handleCrudToolbarClick: function(args, grid, callbacks = {}) {
        const { onAdd, onEdit, onDelete } = callbacks;

        if (args.item.id === 'AddCustom' && onAdd) {
            onAdd();
        }

        if (args.item.id === 'EditCustom' && onEdit) {
            const selectedRecords = grid.getSelectedRecords();
            if (selectedRecords.length > 0) {
                onEdit(selectedRecords[0]);
            }
        }

        if (args.item.id === 'DeleteCustom' && onDelete) {
            const selectedRecords = grid.getSelectedRecords();
            if (selectedRecords.length > 0) {
                onDelete(selectedRecords[0]);
            }
        }
    },

    /**
     * Handle row selection for CRUD operations
     * @param {Object} grid - Grid instance
     * @param {Array} enableItems - Items to enable/disable
     */
    handleRowSelection: function(grid, enableItems = ['EditCustom', 'DeleteCustom']) {
        if (!grid || !grid.toolbarModule) return;

        const selectedRecords = grid.getSelectedRecords();
        const hasSelection = selectedRecords.length > 0;
        
        grid.toolbarModule.enableItems(enableItems, hasSelection);
    }
};

// Export components and utilities for global registration
window.GridComponents = {
    BaseGrid,
    GridUtils
};
