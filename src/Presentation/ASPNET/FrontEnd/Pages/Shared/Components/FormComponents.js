/**
 * FormComponents.js
 * Reusable form components for consistent UI across the application
 * Includes text inputs, dropdowns, textareas, and other form elements
 */

/**
 * SyncfusionTextBox - Wrapper component for Syncfusion TextBox
 */
const SyncfusionTextBox = {
    name: 'SyncfusionTextBox',
    props: {
        modelValue: {
            type: String,
            default: ''
        },
        placeholder: {
            type: String,
            default: ''
        },
        disabled: {
            type: Boolean,
            default: false
        },
        readonly: {
            type: Boolean,
            default: false
        },
        maxLength: {
            type: Number,
            default: null
        },
        type: {
            type: String,
            default: 'text',
            validator: value => ['text', 'password', 'email', 'number'].includes(value)
        }
    },
    emits: ['update:modelValue', 'change', 'focus', 'blur'],
    setup(props, { emit, expose }) {
        const textBoxRef = Vue.ref(null);
        let textBoxObj = null;

        const initializeTextBox = () => {
            if (textBoxRef.value && !textBoxObj) {
                textBoxObj = new ej.inputs.TextBox({
                    placeholder: props.placeholder,
                    value: props.modelValue,
                    enabled: !props.disabled,
                    readonly: props.readonly,
                    type: props.type,
                    maxLength: props.maxLength,
                    change: (args) => {
                        emit('update:modelValue', args.value);
                        emit('change', args.value);
                    },
                    focus: () => emit('focus'),
                    blur: () => emit('blur')
                });
                textBoxObj.appendTo(textBoxRef.value);
            }
        };

        const updateValue = () => {
            if (textBoxObj && textBoxObj.value !== props.modelValue) {
                textBoxObj.value = props.modelValue;
            }
        };

        const focus = () => {
            if (textBoxObj) {
                textBoxObj.focusIn();
            }
        };

        const blur = () => {
            if (textBoxObj) {
                textBoxObj.focusOut();
            }
        };

        // Watch for prop changes
        Vue.watch(() => props.modelValue, updateValue);
        Vue.watch(() => props.disabled, (newVal) => {
            if (textBoxObj) {
                textBoxObj.enabled = !newVal;
            }
        });
        Vue.watch(() => props.readonly, (newVal) => {
            if (textBoxObj) {
                textBoxObj.readonly = newVal;
            }
        });

        Vue.onMounted(() => {
            initializeTextBox();
        });

        Vue.onUnmounted(() => {
            if (textBoxObj) {
                textBoxObj.destroy();
            }
        });

        expose({ focus, blur });

        return {
            textBoxRef
        };
    },
    template: `<input ref="textBoxRef" type="text" />`
};

/**
 * FormField - Wrapper component for form fields with label and error display
 */
const FormField = {
    name: 'FormField',
    props: {
        label: {
            type: String,
            required: true
        },
        error: {
            type: String,
            default: ''
        },
        required: {
            type: Boolean,
            default: false
        },
        colClass: {
            type: String,
            default: 'col-12'
        }
    },
    setup(props, { slots }) {
        return {
            slots
        };
    },
    template: `
        <div :class="colClass">
            <label class="form-label">
                {{ label }}
                <span v-if="required" class="text-danger">*</span>
            </label>
            <slot></slot>
            <div v-if="error" class="text-danger small mt-1">{{ error }}</div>
        </div>
    `
};

/**
 * SyncfusionDropDown - Wrapper component for Syncfusion DropDownList
 */
const SyncfusionDropDown = {
    name: 'SyncfusionDropDown',
    props: {
        modelValue: {
            type: [String, Number, Object],
            default: null
        },
        dataSource: {
            type: Array,
            default: () => []
        },
        fields: {
            type: Object,
            default: () => ({ text: 'text', value: 'value' })
        },
        placeholder: {
            type: String,
            default: 'Select an option'
        },
        disabled: {
            type: Boolean,
            default: false
        },
        allowFiltering: {
            type: Boolean,
            default: false
        },
        width: {
            type: String,
            default: '100%'
        }
    },
    emits: ['update:modelValue', 'change', 'select'],
    setup(props, { emit, expose }) {
        const dropDownRef = Vue.ref(null);
        let dropDownObj = null;

        const initializeDropDown = () => {
            if (dropDownRef.value && !dropDownObj) {
                dropDownObj = new ej.dropdowns.DropDownList({
                    dataSource: props.dataSource,
                    fields: props.fields,
                    placeholder: props.placeholder,
                    value: props.modelValue,
                    enabled: !props.disabled,
                    allowFiltering: props.allowFiltering,
                    width: props.width,
                    change: (args) => {
                        emit('update:modelValue', args.value);
                        emit('change', args.value);
                    },
                    select: (args) => {
                        emit('select', args.itemData);
                    }
                });
                dropDownObj.appendTo(dropDownRef.value);
            }
        };

        const updateValue = () => {
            if (dropDownObj && dropDownObj.value !== props.modelValue) {
                dropDownObj.value = props.modelValue;
            }
        };

        const updateDataSource = () => {
            if (dropDownObj) {
                dropDownObj.dataSource = props.dataSource;
                dropDownObj.dataBind();
            }
        };

        // Watch for prop changes
        Vue.watch(() => props.modelValue, updateValue);
        Vue.watch(() => props.dataSource, updateDataSource, { deep: true });
        Vue.watch(() => props.disabled, (newVal) => {
            if (dropDownObj) {
                dropDownObj.enabled = !newVal;
            }
        });

        Vue.onMounted(() => {
            initializeDropDown();
        });

        Vue.onUnmounted(() => {
            if (dropDownObj) {
                dropDownObj.destroy();
            }
        });

        expose({
            refresh: () => dropDownObj?.dataBind()
        });

        return {
            dropDownRef
        };
    },
    template: `<input ref="dropDownRef" type="text" />`
};

/**
 * TextAreaField - Standard textarea with consistent styling
 */
const TextAreaField = {
    name: 'TextAreaField',
    props: {
        modelValue: {
            type: String,
            default: ''
        },
        placeholder: {
            type: String,
            default: ''
        },
        rows: {
            type: Number,
            default: 3
        },
        disabled: {
            type: Boolean,
            default: false
        },
        readonly: {
            type: Boolean,
            default: false
        },
        maxLength: {
            type: Number,
            default: null
        }
    },
    emits: ['update:modelValue', 'change', 'focus', 'blur'],
    setup(props, { emit }) {
        const handleInput = (event) => {
            emit('update:modelValue', event.target.value);
        };

        const handleChange = (event) => {
            emit('change', event.target.value);
        };

        const handleFocus = () => {
            emit('focus');
        };

        const handleBlur = () => {
            emit('blur');
        };

        return {
            handleInput,
            handleChange,
            handleFocus,
            handleBlur
        };
    },
    template: `
        <textarea
            :value="modelValue"
            @input="handleInput"
            @change="handleChange"
            @focus="handleFocus"
            @blur="handleBlur"
            :placeholder="placeholder"
            :rows="rows"
            :disabled="disabled"
            :readonly="readonly"
            :maxlength="maxLength"
            class="form-control"
        ></textarea>
    `
};

// Export components for global registration
window.FormComponents = {
    SyncfusionTextBox,
    FormField,
    SyncfusionDropDown,
    TextAreaField
};
