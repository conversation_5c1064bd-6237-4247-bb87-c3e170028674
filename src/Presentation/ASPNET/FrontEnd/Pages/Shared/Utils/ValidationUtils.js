/**
 * ValidationUtils.js
 * Common validation utilities for form inputs
 * Provides reusable validation functions across the application
 */

const ValidationUtils = {
    /**
     * Validate required field
     * @param {string} value - Value to validate
     * @param {string} fieldName - Name of the field for error message
     * @returns {string} Error message or empty string if valid
     */
    validateRequired: function(value, fieldName = 'Field') {
        if (!value || value.toString().trim() === '') {
            return `${fieldName} is required.`;
        }
        return '';
    },

    /**
     * Validate minimum length
     * @param {string} value - Value to validate
     * @param {number} minLength - Minimum required length
     * @param {string} fieldName - Name of the field for error message
     * @returns {string} Error message or empty string if valid
     */
    validateMinLength: function(value, minLength, fieldName = 'Field') {
        if (value && value.toString().length < minLength) {
            return `${fieldName} must be at least ${minLength} characters long.`;
        }
        return '';
    },

    /**
     * Validate maximum length
     * @param {string} value - Value to validate
     * @param {number} maxLength - Maximum allowed length
     * @param {string} fieldName - Name of the field for error message
     * @returns {string} Error message or empty string if valid
     */
    validateMaxLength: function(value, maxLength, fieldName = 'Field') {
        if (value && value.toString().length > maxLength) {
            return `${fieldName} must not exceed ${maxLength} characters.`;
        }
        return '';
    },

    /**
     * Validate email format
     * @param {string} email - Email to validate
     * @returns {string} Error message or empty string if valid
     */
    validateEmail: function(email) {
        if (!email) return '';
        
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            return 'Please enter a valid email address.';
        }
        return '';
    },

    /**
     * Validate phone number format
     * @param {string} phone - Phone number to validate
     * @returns {string} Error message or empty string if valid
     */
    validatePhone: function(phone) {
        if (!phone) return '';
        
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
        if (!phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''))) {
            return 'Please enter a valid phone number.';
        }
        return '';
    },

    /**
     * Validate numeric value
     * @param {string|number} value - Value to validate
     * @param {string} fieldName - Name of the field for error message
     * @returns {string} Error message or empty string if valid
     */
    validateNumeric: function(value, fieldName = 'Field') {
        if (value !== '' && value !== null && value !== undefined) {
            if (isNaN(value) || isNaN(parseFloat(value))) {
                return `${fieldName} must be a valid number.`;
            }
        }
        return '';
    },

    /**
     * Validate positive number
     * @param {string|number} value - Value to validate
     * @param {string} fieldName - Name of the field for error message
     * @returns {string} Error message or empty string if valid
     */
    validatePositiveNumber: function(value, fieldName = 'Field') {
        const numericError = this.validateNumeric(value, fieldName);
        if (numericError) return numericError;
        
        if (value !== '' && value !== null && value !== undefined) {
            if (parseFloat(value) <= 0) {
                return `${fieldName} must be a positive number.`;
            }
        }
        return '';
    },

    /**
     * Validate date format
     * @param {string} dateString - Date string to validate
     * @param {string} fieldName - Name of the field for error message
     * @returns {string} Error message or empty string if valid
     */
    validateDate: function(dateString, fieldName = 'Date') {
        if (!dateString) return '';
        
        const date = new Date(dateString);
        if (isNaN(date.getTime())) {
            return `${fieldName} must be a valid date.`;
        }
        return '';
    },

    /**
     * Validate code format (alphanumeric with optional special characters)
     * @param {string} code - Code to validate
     * @param {string} fieldName - Name of the field for error message
     * @returns {string} Error message or empty string if valid
     */
    validateCode: function(code, fieldName = 'Code') {
        if (!code) return '';
        
        const codeRegex = /^[A-Za-z0-9_\-]+$/;
        if (!codeRegex.test(code)) {
            return `${fieldName} can only contain letters, numbers, underscores, and hyphens.`;
        }
        return '';
    },

    /**
     * Validate multiple fields at once
     * @param {Object} fields - Object with field values and validation rules
     * @param {Object} errors - Object to store error messages
     * @returns {boolean} True if all fields are valid
     * 
     * Example usage:
     * const fields = {
     *   regionCode: { value: 'REG001', rules: ['required', 'code'] },
     *   areaCode: { value: 'AREA001', rules: ['required', 'code'] },
     *   description: { value: 'Description', rules: ['maxLength:500'] }
     * };
     * const errors = {};
     * const isValid = ValidationUtils.validateFields(fields, errors);
     */
    validateFields: function(fields, errors) {
        let isValid = true;
        
        for (const [fieldName, fieldConfig] of Object.entries(fields)) {
            errors[fieldName] = '';
            const { value, rules = [] } = fieldConfig;
            
            for (const rule of rules) {
                let errorMessage = '';
                
                if (rule === 'required') {
                    errorMessage = this.validateRequired(value, fieldName);
                } else if (rule === 'email') {
                    errorMessage = this.validateEmail(value);
                } else if (rule === 'phone') {
                    errorMessage = this.validatePhone(value);
                } else if (rule === 'numeric') {
                    errorMessage = this.validateNumeric(value, fieldName);
                } else if (rule === 'positiveNumber') {
                    errorMessage = this.validatePositiveNumber(value, fieldName);
                } else if (rule === 'date') {
                    errorMessage = this.validateDate(value, fieldName);
                } else if (rule === 'code') {
                    errorMessage = this.validateCode(value, fieldName);
                } else if (rule.startsWith('minLength:')) {
                    const minLength = parseInt(rule.split(':')[1]);
                    errorMessage = this.validateMinLength(value, minLength, fieldName);
                } else if (rule.startsWith('maxLength:')) {
                    const maxLength = parseInt(rule.split(':')[1]);
                    errorMessage = this.validateMaxLength(value, maxLength, fieldName);
                }
                
                if (errorMessage) {
                    errors[fieldName] = errorMessage;
                    isValid = false;
                    break; // Stop at first error for this field
                }
            }
        }
        
        return isValid;
    },

    /**
     * Clear all errors in an errors object
     * @param {Object} errors - Errors object to clear
     */
    clearErrors: function(errors) {
        for (const key in errors) {
            if (errors.hasOwnProperty(key)) {
                errors[key] = '';
            }
        }
    },

    /**
     * Check if errors object has any errors
     * @param {Object} errors - Errors object to check
     * @returns {boolean} True if there are any errors
     */
    hasErrors: function(errors) {
        return Object.values(errors).some(error => error !== '');
    }
};
