<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Region Module Test</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Syncfusion CSS -->
    <link href="https://cdn.syncfusion.com/ej2/material.css" rel="stylesheet">
    
    <!-- SweetAlert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid mt-4">
        <h2>Region Module Test</h2>
        <div id="app" v-cloak>
            <div class="row">
                <div class="col-12">
                    <!-- Region Grid Component -->
                    <region-grid 
                        ref="gridRef"
                        :data-source="state.mainData"
                        @add-region="handleAddRegion"
                        @edit-region="handleEditRegion"
                        @delete-region="handleDeleteRegion">
                    </region-grid>
                </div>
            </div>

            <!-- Region Modal Component -->
            <region-modal 
                ref="modalRef"
                :mode="state.modalMode"
                :region-data="state.currentRegion"
                :is-submitting="state.isSubmitting"
                @submit="handleFormSubmit"
                @close="handleModalClose">
            </region-modal>
        </div>
    </div>

    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/bootstrap.bundle.min.js"></script>
    
    <!-- Syncfusion JS -->
    <script src="https://cdn.syncfusion.com/ej2/dist/ej2.min.js"></script>
    
    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Mock dependencies -->
    <script>
        // Mock global functions and objects for testing
        window.getDashminGridHeight = () => 400;
        window.hideSpinnerAndShowContent = () => console.log('Spinner hidden');
        
        window.StorageManager = {
            getUserId: () => 'test-user-123'
        };
        
        window.SecurityManager = {
            validateToken: async () => Promise.resolve(),
            authorizePage: async (permissions) => Promise.resolve()
        };
        
        window.AxiosManager = {
            get: async (url, params) => {
                console.log('Mock GET:', url, params);
                return Promise.resolve({ data: { code: 200, content: { data: [] } } });
            },
            post: async (url, data) => {
                console.log('Mock POST:', url, data);
                return Promise.resolve({ data: { code: 200, message: 'Success' } });
            }
        };
    </script>

    <!-- Load modular components -->
    <script src="./Services/RegionService.js"></script>
    <script src="../Shared/Utils/ValidationUtils.js"></script>
    <script src="../Shared/Components/FormComponents.js"></script>
    <script src="../Shared/Components/GridComponents.js"></script>
    <script src="./Components/RegionGrid.js"></script>
    <script src="./Components/RegionModal.js"></script>
    <script src="./RegionList.cshtml.js"></script>

    <!-- Validation script -->
    <script src="./validate-refactoring.js"></script>

    <style>
        [v-cloak] {
            display: none;
        }
        
        .grid-container {
            margin: 20px 0;
        }
        
        .text-danger {
            color: #dc3545 !important;
        }
        
        .spinner-border-sm {
            width: 1rem;
            height: 1rem;
        }
    </style>
</body>
</html>
