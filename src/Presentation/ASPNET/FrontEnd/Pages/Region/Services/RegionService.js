/**
 * RegionService.js
 * Handles all Region-related API calls and data management
 * Provides mock data for testing the refactored components
 */

const RegionService = {
    // Mock data for testing
    mockData: [
        {
            id: '1',
            regionCode: 'REG001',
            areaCode: 'AREA001',
            description: 'North Region - Primary coverage area for northern districts',
            createdAtUtc: new Date('2024-01-15T08:30:00Z')
        },
        {
            id: '2',
            regionCode: 'REG002',
            areaCode: 'AREA002',
            description: 'South Region - Covers southern metropolitan areas',
            createdAtUtc: new Date('2024-01-20T10:15:00Z')
        },
        {
            id: '3',
            regionCode: 'REG003',
            areaCode: 'AREA003',
            description: 'East Region - Eastern coastal territories',
            createdAtUtc: new Date('2024-02-01T14:45:00Z')
        },
        {
            id: '4',
            regionCode: 'REG004',
            areaCode: 'AREA004',
            description: 'West Region - Western industrial zones',
            createdAtUtc: new Date('2024-02-10T09:20:00Z')
        },
        {
            id: '5',
            regionCode: 'REG005',
            areaCode: 'AREA005',
            description: 'Central Region - Downtown and central business district',
            createdAtUtc: new Date('2024-02-15T16:30:00Z')
        }
    ],

    // Simulate API delay
    simulateDelay: (ms = 500) => {
        return new Promise(resolve => setTimeout(resolve, ms));
    },

    // Mock API response structure
    createApiResponse: (data, success = true, message = '') => {
        return {
            data: {
                code: success ? 200 : 400,
                message: message || (success ? 'Operation successful' : 'Operation failed'),
                content: {
                    data: data
                }
            }
        };
    },

    /**
     * Get all regions
     * @returns {Promise} API response with region list
     */
    getRegionList: async function() {
        try {
            await this.simulateDelay();
            
            // Simulate occasional API failure for testing
            if (Math.random() < 0.05) { // 5% chance of failure
                throw new Error('Network error');
            }

            return this.createApiResponse(this.mockData);
        } catch (error) {
            console.error('RegionService.getRegionList error:', error);
            throw error;
        }
    },

    /**
     * Create a new region
     * @param {string} areaCode - Area code for the region
     * @param {string} regionCode - Region code
     * @param {string} description - Region description
     * @param {string} createdById - ID of the user creating the region
     * @returns {Promise} API response
     */
    createRegion: async function(areaCode, regionCode, description, createdById) {
        try {
            await this.simulateDelay();

            // Validate required fields
            if (!areaCode || !regionCode) {
                return this.createApiResponse(null, false, 'Area Code and Region Code are required');
            }

            // Check for duplicate region code
            const existingRegion = this.mockData.find(r => r.regionCode === regionCode);
            if (existingRegion) {
                return this.createApiResponse(null, false, 'Region Code already exists');
            }

            // Create new region
            const newRegion = {
                id: (this.mockData.length + 1).toString(),
                regionCode,
                areaCode,
                description: description || '',
                createdAtUtc: new Date()
            };

            this.mockData.push(newRegion);
            return this.createApiResponse(newRegion);
        } catch (error) {
            console.error('RegionService.createRegion error:', error);
            throw error;
        }
    },

    /**
     * Update an existing region
     * @param {string} id - Region ID
     * @param {string} areaCode - Area code for the region
     * @param {string} regionCode - Region code
     * @param {string} description - Region description
     * @param {string} updatedById - ID of the user updating the region
     * @returns {Promise} API response
     */
    updateRegion: async function(id, areaCode, regionCode, description, updatedById) {
        try {
            await this.simulateDelay();

            // Validate required fields
            if (!areaCode || !regionCode) {
                return this.createApiResponse(null, false, 'Area Code and Region Code are required');
            }

            // Find the region to update
            const regionIndex = this.mockData.findIndex(r => r.id === id);
            if (regionIndex === -1) {
                return this.createApiResponse(null, false, 'Region not found');
            }

            // Check for duplicate region code (excluding current region)
            const existingRegion = this.mockData.find(r => r.regionCode === regionCode && r.id !== id);
            if (existingRegion) {
                return this.createApiResponse(null, false, 'Region Code already exists');
            }

            // Update the region
            this.mockData[regionIndex] = {
                ...this.mockData[regionIndex],
                regionCode,
                areaCode,
                description: description || '',
                updatedAtUtc: new Date()
            };

            return this.createApiResponse(this.mockData[regionIndex]);
        } catch (error) {
            console.error('RegionService.updateRegion error:', error);
            throw error;
        }
    },

    /**
     * Delete a region
     * @param {string} id - Region ID
     * @param {string} deletedById - ID of the user deleting the region
     * @returns {Promise} API response
     */
    deleteRegion: async function(id, deletedById) {
        try {
            await this.simulateDelay();

            // Find the region to delete
            const regionIndex = this.mockData.findIndex(r => r.id === id);
            if (regionIndex === -1) {
                return this.createApiResponse(null, false, 'Region not found');
            }

            // Remove the region
            const deletedRegion = this.mockData.splice(regionIndex, 1)[0];
            return this.createApiResponse(deletedRegion);
        } catch (error) {
            console.error('RegionService.deleteRegion error:', error);
            throw error;
        }
    },

    /**
     * Reset mock data to initial state (for testing purposes)
     */
    resetMockData: function() {
        this.mockData.length = 0;
        this.mockData.push(
            {
                id: '1',
                regionCode: 'REG001',
                areaCode: 'AREA001',
                description: 'North Region - Primary coverage area for northern districts',
                createdAtUtc: new Date('2024-01-15T08:30:00Z')
            },
            {
                id: '2',
                regionCode: 'REG002',
                areaCode: 'AREA002',
                description: 'South Region - Covers southern metropolitan areas',
                createdAtUtc: new Date('2024-01-20T10:15:00Z')
            },
            {
                id: '3',
                regionCode: 'REG003',
                areaCode: 'AREA003',
                description: 'East Region - Eastern coastal territories',
                createdAtUtc: new Date('2024-02-01T14:45:00Z')
            },
            {
                id: '4',
                regionCode: 'REG004',
                areaCode: 'AREA004',
                description: 'West Region - Western industrial zones',
                createdAtUtc: new Date('2024-02-10T09:20:00Z')
            },
            {
                id: '5',
                regionCode: 'REG005',
                areaCode: 'AREA005',
                description: 'Central Region - Downtown and central business district',
                createdAtUtc: new Date('2024-02-15T16:30:00Z')
            }
        );
    }
};

// For production, replace mock methods with actual API calls
// Example:
// RegionService.getRegionList = async function() {
//     try {
//         const response = await AxiosManager.get('/Region/GetRegionList', {});
//         return response;
//     } catch (error) {
//         throw error;
//     }
// };
