/**
 * validate-refactoring.js
 * Simple validation script to check the refactored Region module
 * Run this in the browser console on the test page
 */

const validateRefactoring = {
    /**
     * Check if all required files are loaded
     */
    checkFilesLoaded: function() {
        const checks = {
            'RegionService': typeof RegionService !== 'undefined',
            'ValidationUtils': typeof ValidationUtils !== 'undefined',
            'FormComponents': typeof window.FormComponents !== 'undefined',
            'GridComponents': typeof window.GridComponents !== 'undefined',
            'RegionGrid': typeof RegionGrid !== 'undefined',
            'RegionModal': typeof RegionModal !== 'undefined',
            'Vue': typeof Vue !== 'undefined',
            'Bootstrap': typeof bootstrap !== 'undefined',
            'Syncfusion': typeof ej !== 'undefined',
            'SweetAlert': typeof Swal !== 'undefined'
        };

        console.log('=== File Loading Check ===');
        let allLoaded = true;
        for (const [name, loaded] of Object.entries(checks)) {
            const status = loaded ? '✅' : '❌';
            console.log(`${status} ${name}: ${loaded ? 'Loaded' : 'Missing'}`);
            if (!loaded) allLoaded = false;
        }
        
        return allLoaded;
    },

    /**
     * Test RegionService functionality
     */
    testRegionService: async function() {
        console.log('\n=== RegionService Test ===');
        
        try {
            // Test getRegionList
            console.log('Testing getRegionList...');
            const listResponse = await RegionService.getRegionList();
            console.log('✅ getRegionList successful:', listResponse.data.content.data.length, 'regions');

            // Test createRegion
            console.log('Testing createRegion...');
            const createResponse = await RegionService.createRegion('TEST001', 'TESTREGION', 'Test Region Description', 'test-user');
            console.log('✅ createRegion successful:', createResponse.data.code === 200);

            // Test updateRegion
            console.log('Testing updateRegion...');
            const updateResponse = await RegionService.updateRegion('1', 'UPDATED001', 'UPDATEDREGION', 'Updated Description', 'test-user');
            console.log('✅ updateRegion successful:', updateResponse.data.code === 200);

            // Test deleteRegion
            console.log('Testing deleteRegion...');
            const deleteResponse = await RegionService.deleteRegion('1', 'test-user');
            console.log('✅ deleteRegion successful:', deleteResponse.data.code === 200);

            return true;
        } catch (error) {
            console.error('❌ RegionService test failed:', error);
            return false;
        }
    },

    /**
     * Test ValidationUtils functionality
     */
    testValidationUtils: function() {
        console.log('\n=== ValidationUtils Test ===');
        
        try {
            // Test required validation
            const requiredTest = ValidationUtils.validateRequired('', 'Test Field');
            console.log('✅ Required validation:', requiredTest.includes('required'));

            // Test email validation
            const emailTest = ValidationUtils.validateEmail('invalid-email');
            console.log('✅ Email validation:', emailTest.includes('valid email'));

            // Test numeric validation
            const numericTest = ValidationUtils.validateNumeric('abc', 'Number Field');
            console.log('✅ Numeric validation:', numericTest.includes('valid number'));

            // Test batch validation
            const fields = {
                regionCode: { value: '', rules: ['required'] },
                areaCode: { value: 'AREA001', rules: ['required', 'code'] }
            };
            const errors = {};
            const isValid = ValidationUtils.validateFields(fields, errors);
            console.log('✅ Batch validation:', !isValid && errors.regionCode !== '');

            return true;
        } catch (error) {
            console.error('❌ ValidationUtils test failed:', error);
            return false;
        }
    },

    /**
     * Test component structure
     */
    testComponentStructure: function() {
        console.log('\n=== Component Structure Test ===');
        
        try {
            // Check RegionGrid component
            const gridComponent = RegionGrid;
            console.log('✅ RegionGrid component:', typeof gridComponent === 'object' && gridComponent.name === 'RegionGrid');

            // Check RegionModal component
            const modalComponent = RegionModal;
            console.log('✅ RegionModal component:', typeof modalComponent === 'object' && modalComponent.name === 'RegionModal');

            // Check FormComponents
            const formComponents = window.FormComponents;
            console.log('✅ FormComponents available:', typeof formComponents === 'object');
            console.log('  - SyncfusionTextBox:', typeof formComponents.SyncfusionTextBox === 'object');
            console.log('  - FormField:', typeof formComponents.FormField === 'object');
            console.log('  - SyncfusionDropDown:', typeof formComponents.SyncfusionDropDown === 'object');
            console.log('  - TextAreaField:', typeof formComponents.TextAreaField === 'object');

            // Check GridComponents
            const gridComponents = window.GridComponents;
            console.log('✅ GridComponents available:', typeof gridComponents === 'object');
            console.log('  - BaseGrid:', typeof gridComponents.BaseGrid === 'object');
            console.log('  - GridUtils:', typeof gridComponents.GridUtils === 'object');

            return true;
        } catch (error) {
            console.error('❌ Component structure test failed:', error);
            return false;
        }
    },

    /**
     * Test Vue app initialization
     */
    testVueApp: function() {
        console.log('\n=== Vue App Test ===');
        
        try {
            // Check if Vue app is mounted
            const appElement = document.getElementById('app');
            console.log('✅ App element found:', appElement !== null);

            // Check if Vue instance exists
            const vueInstance = appElement?.__vue_app__;
            console.log('✅ Vue instance:', vueInstance !== undefined);

            return true;
        } catch (error) {
            console.error('❌ Vue app test failed:', error);
            return false;
        }
    },

    /**
     * Run all validation tests
     */
    runAllTests: async function() {
        console.log('🚀 Starting Region Module Validation Tests...\n');
        
        const results = {
            filesLoaded: this.checkFilesLoaded(),
            regionService: await this.testRegionService(),
            validationUtils: this.testValidationUtils(),
            componentStructure: this.testComponentStructure(),
            vueApp: this.testVueApp()
        };

        console.log('\n=== Test Results Summary ===');
        let allPassed = true;
        for (const [test, passed] of Object.entries(results)) {
            const status = passed ? '✅ PASS' : '❌ FAIL';
            console.log(`${status} ${test}`);
            if (!passed) allPassed = false;
        }

        console.log(`\n🎯 Overall Result: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
        
        if (allPassed) {
            console.log('\n🎉 Refactoring validation successful! The modular structure is working correctly.');
        } else {
            console.log('\n⚠️ Some issues detected. Please review the failed tests above.');
        }

        return allPassed;
    }
};

// Auto-run tests if this script is loaded in a browser
if (typeof window !== 'undefined') {
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => validateRefactoring.runAllTests(), 1000);
        });
    } else {
        setTimeout(() => validateRefactoring.runAllTests(), 1000);
    }
}

// Export for manual testing
window.validateRefactoring = validateRefactoring;
