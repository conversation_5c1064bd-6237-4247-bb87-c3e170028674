﻿@page
@{
    ViewData["Title"] = "Region List";
}

<div id="app" v-cloak>
    <div class="row">
        <div class="col-12">
            <!-- Region Grid Component -->
            <region-grid
                ref="gridRef"
                :data-source="state.mainData"
                @@add-region="handleAddRegion"
                @@edit-region="handleEditRegion"
                @@delete-region="handleDeleteRegion">
            </region-grid>
        </div>
    </div>

    <!-- Region Modal Component -->
    <region-modal
        ref="modalRef"
        :mode="state.modalMode"
        :region-data="state.currentRegion"
        :is-submitting="state.isSubmitting"
        @@submit="handleFormSubmit"
        @@close="handleModalClose">
    </region-modal>
</div>

@section scripts {
    <!-- Shared utility components -->
    <script src="~/FrontEnd/Pages/Shared/Utils/ValidationUtils.js"></script>
    <script src="~/FrontEnd/Pages/Shared/Components/FormComponents.js"></script>
    <script src="~/FrontEnd/Pages/Shared/Components/GridComponents.js"></script>

    <!-- Region-specific service -->
    <script src="~/FrontEnd/Pages/Region/Services/RegionService.js"></script>

    <!-- Region-specific components -->
    <script src="~/FrontEnd/Pages/Region/Components/RegionGrid.js"></script>
    <script src="~/FrontEnd/Pages/Region/Components/RegionModal.js"></script>

    <!-- Main page script -->
    <script src="~/FrontEnd/Pages/Region/RegionList.cshtml.js"></script>
}