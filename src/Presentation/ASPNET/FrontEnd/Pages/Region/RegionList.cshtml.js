/**
 * RegionList.cshtml.js
 * Main Vue application for Region List page
 * Orchestrates the modular components and handles overall page logic
 */

const App = {
    setup() {
        // Reactive state for the entire application
        const state = Vue.reactive({
            mainData: [],
            isLoading: false,
            
            // Modal state
            modalVisible: false,
            modalMode: 'add', // 'add', 'edit', 'delete'
            currentRegion: {
                id: '',
                regionCode: '',
                areaCode: '',
                description: ''
            },
            isSubmitting: false
        });

        // Template refs
        const gridRef = Vue.ref(null);
        const modalRef = Vue.ref(null);

        /**
         * Load region data from service
         */
        const loadRegionData = async () => {
            try {
                state.isLoading = true;
                const response = await RegionService.getRegionList();
                
                if (response?.data?.content?.data) {
                    // Format the data to ensure dates are properly handled
                    const formattedData = response.data.content.data.map(item => ({
                        ...item,
                        createdAtUtc: new Date(item.createdAtUtc)
                    }));
                    state.mainData = formattedData;
                }
            } catch (error) {
                console.error('Error loading region data:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Error Loading Data',
                    text: 'Failed to load region data. Please try again.',
                    confirmButtonText: 'OK'
                });
            } finally {
                state.isLoading = false;
            }
        };

        /**
         * Handle add region action
         */
        const handleAddRegion = () => {
            state.modalMode = 'add';
            state.currentRegion = {
                id: '',
                regionCode: '',
                areaCode: '',
                description: ''
            };
            modalRef.value?.show();
        };

        /**
         * Handle edit region action
         */
        const handleEditRegion = (regionData) => {
            state.modalMode = 'edit';
            state.currentRegion = {
                id: regionData.id || '',
                regionCode: regionData.regionCode || '',
                areaCode: regionData.areaCode || '',
                description: regionData.description || ''
            };
            modalRef.value?.show();
        };

        /**
         * Handle delete region action
         */
        const handleDeleteRegion = (regionData) => {
            state.modalMode = 'delete';
            state.currentRegion = {
                id: regionData.id || '',
                regionCode: regionData.regionCode || '',
                areaCode: regionData.areaCode || '',
                description: regionData.description || ''
            };
            modalRef.value?.show();
        };

        /**
         * Handle form submission from modal
         */
        const handleFormSubmit = async (formData) => {
            try {
                state.isSubmitting = true;
                
                // Add a small delay for better UX
                await new Promise(resolve => setTimeout(resolve, 300));

                let response;
                const userId = StorageManager.getUserId();

                switch (state.modalMode) {
                    case 'add':
                        response = await RegionService.createRegion(
                            formData.areaCode,
                            formData.regionCode,
                            formData.description,
                            userId
                        );
                        break;
                    
                    case 'edit':
                        response = await RegionService.updateRegion(
                            formData.id,
                            formData.areaCode,
                            formData.regionCode,
                            formData.description,
                            userId
                        );
                        break;
                    
                    case 'delete':
                        response = await RegionService.deleteRegion(
                            formData.id,
                            userId
                        );
                        break;
                }

                if (response?.data?.code === 200) {
                    // Reload data and refresh grid
                    await loadRegionData();
                    gridRef.value?.refreshGrid();

                    // Show success message
                    const actionText = state.modalMode === 'delete' ? 'Delete' : 'Save';
                    Swal.fire({
                        icon: 'success',
                        title: `${actionText} Successful`,
                        text: 'Form will be closed...',
                        timer: 2000,
                        showConfirmButton: false
                    });

                    // Close modal after delay
                    setTimeout(() => {
                        modalRef.value?.hide();
                    }, 2000);
                } else {
                    // Show error message
                    const actionText = state.modalMode === 'delete' ? 'Delete' : 'Save';
                    Swal.fire({
                        icon: 'error',
                        title: `${actionText} Failed`,
                        text: response?.data?.message || 'Please check your data.',
                        confirmButtonText: 'Try Again'
                    });
                }
            } catch (error) {
                console.error('Form submission error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'An Error Occurred',
                    text: error.response?.data?.message || 'Please try again.',
                    confirmButtonText: 'OK'
                });
            } finally {
                state.isSubmitting = false;
            }
        };

        /**
         * Handle modal close
         */
        const handleModalClose = () => {
            state.modalVisible = false;
            // Clear any form state if needed
        };

        /**
         * Initialize the application
         */
        const initializeApp = async () => {
            try {
                // Validate user authentication
                await SecurityManager.validateToken();
                
                // Uncomment the following line if authorization is needed
                // await SecurityManager.authorizePage(['Regions']);
                
                // Load initial data
                await loadRegionData();
            } catch (error) {
                console.error('App initialization error:', error);
                // Handle authentication/authorization errors
                if (error.response?.status === 401) {
                    window.location.href = '/Account/Login';
                }
            } finally {
                // Hide loading spinner and show content
                if (typeof hideSpinnerAndShowContent === 'function') {
                    hideSpinnerAndShowContent();
                }
            }
        };

        // Lifecycle hooks
        Vue.onMounted(async () => {
            await initializeApp();
        });

        // Return reactive state and methods for template
        return {
            state,
            gridRef,
            modalRef,
            handleAddRegion,
            handleEditRegion,
            handleDeleteRegion,
            handleFormSubmit,
            handleModalClose
        };
    },
    components: {
        RegionGrid,
        RegionModal
    }
};

// Mount the Vue application
Vue.createApp(App).mount('#app');
