/**
 * RegionModal.js
 * Vue component for the Region form modal
 * Handles add/edit/delete operations with form validation
 */

const RegionModal = {
    name: 'RegionModal',
    props: {
        isVisible: {
            type: Boolean,
            default: false
        },
        mode: {
            type: String,
            default: 'add', // 'add', 'edit', 'delete'
            validator: value => ['add', 'edit', 'delete'].includes(value)
        },
        regionData: {
            type: Object,
            default: () => ({
                id: '',
                regionCode: '',
                areaCode: '',
                description: ''
            })
        },
        isSubmitting: {
            type: Boolean,
            default: false
        }
    },
    emits: ['close', 'submit'],
    setup(props, { emit, expose }) {
        const modalRef = Vue.ref(null);
        const regionCodeRef = Vue.ref(null);
        const areaCodeRef = Vue.ref(null);
        
        let modalObj = null;
        let regionCodeTextBox = null;
        let areaCodeTextBox = null;

        // Form state
        const formState = Vue.reactive({
            id: '',
            regionCode: '',
            areaCode: '',
            description: '',
            errors: {
                regionCode: '',
                areaCode: '',
                description: ''
            }
        });

        // Computed properties
        const modalTitle = Vue.computed(() => {
            switch (props.mode) {
                case 'add': return 'Add Region';
                case 'edit': return 'Edit Region';
                case 'delete': return 'Delete Region?';
                default: return 'Region';
            }
        });

        const submitButtonText = Vue.computed(() => {
            if (props.isSubmitting) {
                return props.mode === 'delete' ? 'Deleting...' : 'Saving...';
            }
            return props.mode === 'delete' ? 'Delete' : 'Save';
        });

        const submitButtonClass = Vue.computed(() => {
            return props.mode === 'delete' ? 'btn-danger' : 'btn-primary';
        });

        /**
         * Initialize Bootstrap modal
         */
        const initializeModal = () => {
            if (modalRef.value && !modalObj) {
                modalObj = new bootstrap.Modal(modalRef.value, {
                    backdrop: 'static',
                    keyboard: false
                });

                modalRef.value.addEventListener('hidden.bs.modal', handleModalHidden);
            }
        };

        /**
         * Initialize Syncfusion TextBox components
         */
        const initializeTextBoxes = () => {
            if (regionCodeRef.value && !regionCodeTextBox) {
                regionCodeTextBox = new ej.inputs.TextBox({
                    placeholder: 'Enter Region Code',
                });
                regionCodeTextBox.appendTo(regionCodeRef.value);
            }

            if (areaCodeRef.value && !areaCodeTextBox) {
                areaCodeTextBox = new ej.inputs.TextBox({
                    placeholder: 'Enter Area Code',
                });
                areaCodeTextBox.appendTo(areaCodeRef.value);
            }
        };

        /**
         * Update form state with region data
         */
        const updateFormState = () => {
            formState.id = props.regionData.id || '';
            formState.regionCode = props.regionData.regionCode || '';
            formState.areaCode = props.regionData.areaCode || '';
            formState.description = props.regionData.description || '';
            
            // Clear errors
            formState.errors.regionCode = '';
            formState.errors.areaCode = '';
            formState.errors.description = '';

            // Update TextBox values
            if (regionCodeTextBox) {
                regionCodeTextBox.value = formState.regionCode;
            }
            if (areaCodeTextBox) {
                areaCodeTextBox.value = formState.areaCode;
            }
        };

        /**
         * Validate form data
         */
        const validateForm = () => {
            formState.errors.regionCode = '';
            formState.errors.areaCode = '';
            formState.errors.description = '';

            let isValid = true;

            if (!formState.regionCode.trim()) {
                formState.errors.regionCode = 'Region Code is required.';
                isValid = false;
            }

            if (!formState.areaCode.trim()) {
                formState.errors.areaCode = 'Area Code is required.';
                isValid = false;
            }

            return isValid;
        };

        /**
         * Handle form submission
         */
        const handleSubmit = () => {
            if (props.mode !== 'delete' && !validateForm()) {
                return;
            }

            const submitData = {
                id: formState.id,
                regionCode: formState.regionCode,
                areaCode: formState.areaCode,
                description: formState.description,
                mode: props.mode
            };

            emit('submit', submitData);
        };

        /**
         * Handle modal hidden event
         */
        const handleModalHidden = () => {
            formState.errors.regionCode = '';
            formState.errors.areaCode = '';
            formState.errors.description = '';
            emit('close');
        };

        /**
         * Show modal
         */
        const show = () => {
            if (modalObj) {
                updateFormState();
                modalObj.show();
            }
        };

        /**
         * Hide modal
         */
        const hide = () => {
            if (modalObj) {
                modalObj.hide();
            }
        };

        // Watch for region data changes
        Vue.watch(
            () => props.regionData,
            () => {
                updateFormState();
            },
            { deep: true }
        );

        // Watch for form field changes to clear errors
        Vue.watch(
            () => formState.regionCode,
            () => {
                formState.errors.regionCode = '';
                if (regionCodeTextBox) {
                    regionCodeTextBox.value = formState.regionCode;
                }
            }
        );

        Vue.watch(
            () => formState.areaCode,
            () => {
                formState.errors.areaCode = '';
                if (areaCodeTextBox) {
                    areaCodeTextBox.value = formState.areaCode;
                }
            }
        );

        Vue.onMounted(() => {
            initializeModal();
            initializeTextBoxes();
        });

        Vue.onUnmounted(() => {
            if (modalRef.value) {
                modalRef.value.removeEventListener('hidden.bs.modal', handleModalHidden);
            }
            if (modalObj) {
                modalObj.dispose();
            }
            if (regionCodeTextBox) {
                regionCodeTextBox.destroy();
            }
            if (areaCodeTextBox) {
                areaCodeTextBox.destroy();
            }
        });

        // Expose methods for parent component
        expose({
            show,
            hide
        });

        return {
            modalRef,
            regionCodeRef,
            areaCodeRef,
            formState,
            modalTitle,
            submitButtonText,
            submitButtonClass,
            handleSubmit
        };
    },
    template: `
        <div class="modal fade" ref="modalRef" id="RegionModal" tabindex="-1" aria-hidden="true" data-bs-backdrop="static">
            <div class="modal-dialog modal-dialog-centered modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">{{ modalTitle }}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body bg-body-tertiary">
                        <form id="RegionForm">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5>Region Info</h5>
                                        </div>
                                        <div class="card-body bg-body-tertiary">
                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <label for="Region Code">Region Code</label>
                                                    <input ref="regionCodeRef" type="text" v-model="formState.regionCode" :disabled="mode === 'delete'">
                                                    <label class="text-danger">{{ formState.errors.regionCode }}</label>
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="Area Code">Area Code</label>
                                                    <input ref="areaCodeRef" type="text" v-model="formState.areaCode" :disabled="mode === 'delete'">
                                                    <label class="text-danger">{{ formState.errors.areaCode }}</label>
                                                </div>
                                            </div>
                                            <div class="row mb-3">
                                                <div class="col-12">
                                                    <label for="Description">Description</label>
                                                    <textarea id="Description" v-model="formState.description" class="form-control" rows="3" :disabled="mode === 'delete'"></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="button"
                                class="btn"
                                :class="submitButtonClass"
                                @click="handleSubmit"
                                :disabled="isSubmitting">
                            <span class="spinner-border spinner-border-sm me-2" v-if="isSubmitting" role="status" aria-hidden="true"></span>
                            <span>{{ submitButtonText }}</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `
};
