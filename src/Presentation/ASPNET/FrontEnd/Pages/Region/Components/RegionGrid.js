/**
 * RegionGrid.js
 * Vue component for the Region data grid using Syncfusion Grid
 * Handles grid display, toolbar actions, and row selection
 */

const RegionGrid = {
    name: 'RegionGrid',
    props: {
        dataSource: {
            type: Array,
            default: () => []
        }
    },
    emits: ['add-region', 'edit-region', 'delete-region'],
    setup(props, { emit, expose }) {
        const gridRef = Vue.ref(null);
        let gridObj = null;

        /**
         * Create and initialize the Syncfusion Grid
         */
        const createGrid = async () => {
            if (!gridRef.value) return;

            gridObj = new ej.grids.Grid({
                height: getDashminGridHeight(),
                dataSource: props.dataSource,
                allowFiltering: true,
                allowSorting: true,
                allowSelection: true,
                allowGrouping: true,
                allowTextWrap: true,
                allowResizing: true,
                allowPaging: true,
                allowExcelExport: true,
                filterSettings: { type: 'CheckBox' },
                sortSettings: { columns: [{ field: 'createdAtUtc', direction: 'Descending' }] },
                pageSettings: { currentPage: 1, pageSize: 50, pageSizes: ["10", "20", "50", "100", "200", "All"] },
                selectionSettings: { persistSelection: true, type: 'Single' },
                autoFit: true,
                showColumnMenu: true,
                gridLines: 'Horizontal',
                columns: [
                    { type: 'checkbox', width: 60 },
                    {
                        field: 'id', isPrimaryKey: true, headerText: 'Id', visible: false
                    },
                    { field: 'regionCode', headerText: 'Region Code', width: 200, minWidth: 200 },
                    {
                        field: 'areaCode',
                        headerText: 'Area Code',
                        width: 200,
                        minWidth: 200
                    },
                    { field: 'description', headerText: 'Description', width: 400, minWidth: 400 },
                    { field: 'createdAtUtc', headerText: 'Created At UTC', width: 150, format: 'yyyy-MM-dd HH:mm' }
                ],
                toolbar: [
                    'ExcelExport', 'Search',
                    { type: 'Separator' },
                    { text: 'Add', tooltipText: 'Add', prefixIcon: 'e-add', id: 'AddCustom' },
                    { text: 'Edit', tooltipText: 'Edit', prefixIcon: 'e-edit', id: 'EditCustom' },
                    { text: 'Delete', tooltipText: 'Delete', prefixIcon: 'e-delete', id: 'DeleteCustom' },
                    { type: 'Separator' },
                ],
                beforeDataBound: () => { },
                dataBound: function () {
                    gridObj.toolbarModule.enableItems(['EditCustom', 'DeleteCustom'], false);
                    gridObj.autoFitColumns(['areaCode', 'regionCode', 'description', 'createdAtUtc']);
                },
                excelExportComplete: () => { },
                rowSelected: () => {
                    if (gridObj.getSelectedRecords().length === 1) {
                        gridObj.toolbarModule.enableItems(['EditCustom', 'DeleteCustom'], true);
                    } else {
                        gridObj.toolbarModule.enableItems(['EditCustom', 'DeleteCustom'], false);
                    }
                },
                rowDeselected: () => {
                    if (gridObj.getSelectedRecords().length === 1) {
                        gridObj.toolbarModule.enableItems(['EditCustom', 'DeleteCustom'], true);
                    } else {
                        gridObj.toolbarModule.enableItems(['EditCustom', 'DeleteCustom'], false);
                    }
                },
                rowSelecting: () => {
                    if (gridObj.getSelectedRecords().length) {
                        gridObj.clearSelection();
                    }
                },
                toolbarClick: (args) => {
                    if (args.item.id === 'MainGrid_excelexport') {
                        gridObj.excelExport();
                    }   

                    if (args.item.id === 'AddCustom') {
                        emit('add-region');
                    }

                    if (args.item.id === 'EditCustom') {
                        if (gridObj.getSelectedRecords().length) {
                            const selectedRecord = gridObj.getSelectedRecords()[0];
                            emit('edit-region', selectedRecord);
                        }
                    }

                    if (args.item.id === 'DeleteCustom') {
                        if (gridObj.getSelectedRecords().length) {
                            const selectedRecord = gridObj.getSelectedRecords()[0];
                            emit('delete-region', selectedRecord);
                        }
                    }
                }
            });

            gridObj.appendTo(gridRef.value);
        };

        /**
         * Refresh grid data
         */
        const refreshGrid = () => {
            if (gridObj) {
                gridObj.setProperties({ dataSource: props.dataSource });
            }
        };

        /**
         * Get selected records
         */
        const getSelectedRecords = () => {
            return gridObj ? gridObj.getSelectedRecords() : [];
        };

        /**
         * Clear selection
         */
        const clearSelection = () => {
            if (gridObj) {
                gridObj.clearSelection();
            }
        };

        // Watch for data source changes
        Vue.watch(
            () => props.dataSource,
            () => {
                refreshGrid();
            },
            { deep: true }
        );

        Vue.onMounted(async () => {
            await createGrid();
        });

        Vue.onUnmounted(() => {
            if (gridObj) {
                gridObj.destroy();
            }
        });

        // Expose methods for parent component
        expose({
            refreshGrid,
            getSelectedRecords,
            clearSelection
        });

        return {
            gridRef
        };
    },
    template: `
        <div class="grid-container">
            <div ref="gridRef"></div>
        </div>
    `
};
