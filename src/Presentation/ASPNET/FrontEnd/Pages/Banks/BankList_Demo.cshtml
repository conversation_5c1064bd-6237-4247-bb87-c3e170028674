@page
@{
    ViewData["Title"] = "Banks - Component Demo";
}

<!-- Demo showing how the same FormComponents are reused in different feature -->
<div id="bankApp">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <h4 class="mb-4">Bank Management - Reusing Shared Components</h4>
                
                <!-- Bank Details Form - Uses same FormComponents as Contract Entry -->
                <bank-details-form 
                    v-bind:form-data="bankForm"
                    v-bind:errors="formErrors"
                    v-bind:status-options="statusOptions">
                </bank-details-form>

                <!-- Bank Account Form -->
                <bank-account-form 
                    v-bind:form-data="bankForm"
                    v-bind:account-types="accountTypes"
                    v-bind:currencies="currencies">
                </bank-account-form>

                <!-- Action Buttons -->
                <div class="card">
                    <div class="card-body">
                        <button type="button" class="btn btn-primary me-2" v-on:click="saveBank">
                            Save Bank
                        </button>
                        <button type="button" class="btn btn-secondary" v-on:click="resetForm">
                            Reset
                        </button>
                        <button type="button" class="btn btn-info" v-on:click="validateForm">
                            Validate
                        </button>
                    </div>
                </div>

                <!-- Debug Info -->
                <div class="card mt-3" v-if="showDebug">
                    <div class="card-header">
                        <h6>Debug - Form Data</h6>
                    </div>
                    <div class="card-body">
                        <pre v-text="JSON.stringify(bankForm, null, 2)"></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <!-- Load shared components first (same as CustomerContractEntry) -->
    <script src="~/FrontEnd/Components/Shared/FormComponents.js"></script>
    
    <!-- Load Banks feature components -->
    <script src="~/FrontEnd/Components/Banks/BankFormComponents.js"></script>
    
    <!-- Load component loader utility -->
    <script src="~/FrontEnd/Pages/Shared/ComponentLoader.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize the Vue app - demonstrates reusing shared components in different feature
            const app = ComponentLoader.initializeFeatureApp('Bank', {
                data() {
                    return {
                        showDebug: false,
                        
                        bankForm: {
                            // Bank Details
                            bankCode: '',
                            bankName: '',
                            branchCode: '',
                            branchName: '',
                            address: '',
                            contactPerson: '',
                            phone: '',
                            email: '',
                            status: '',
                            establishedDate: '',
                            
                            // Account Details
                            accountNumber: '',
                            accountType: '',
                            currency: '',
                            openingBalance: ''
                        },
                        
                        formErrors: {},
                        
                        statusOptions: [
                            { value: 'active', text: 'Active' },
                            { value: 'inactive', text: 'Inactive' },
                            { value: 'suspended', text: 'Suspended' }
                        ],
                        
                        accountTypes: [
                            { value: 'savings', text: 'Savings Account' },
                            { value: 'current', text: 'Current Account' },
                            { value: 'fixed', text: 'Fixed Deposit' }
                        ],
                        
                        currencies: [
                            { code: 'MYR', name: 'Malaysian Ringgit' },
                            { code: 'USD', name: 'US Dollar' },
                            { code: 'SGD', name: 'Singapore Dollar' }
                        ]
                    };
                },
                
                methods: {
                    validateForm() {
                        this.formErrors = {};
                        
                        // Simple validation example
                        if (!this.bankForm.bankCode) {
                            this.formErrors.bankCode = 'Bank Code is required';
                        }
                        
                        if (!this.bankForm.bankName) {
                            this.formErrors.bankName = 'Bank Name is required';
                        }
                        
                        if (this.bankForm.email && !this.isValidEmail(this.bankForm.email)) {
                            this.formErrors.email = 'Invalid email format';
                        }
                        
                        const hasErrors = Object.keys(this.formErrors).length > 0;
                        if (hasErrors) {
                            alert('Please fix validation errors');
                        } else {
                            alert('Form is valid!');
                        }
                        
                        return !hasErrors;
                    },
                    
                    isValidEmail(email) {
                        // Simple email validation without regex to avoid Razor conflicts
                        if (!email) return false;
                        const parts = email.split('@@');
                        if (parts.length !== 2) return false;
                        const domain = parts[1];
                        return domain && domain.includes('.') && parts[0].length > 0;
                    },
                    
                    saveBank() {
                        if (this.validateForm()) {
                            console.log('Saving bank:', this.bankForm);
                            // Here you would call your API endpoint
                            alert('Bank saved successfully! (Demo)');
                        }
                    },
                    
                    resetForm() {
                        // Reset form to defaults
                        Object.keys(this.bankForm).forEach(key => {
                            this.bankForm[key] = '';
                        });
                        this.formErrors = {};
                    },
                    
                    toggleDebug() {
                        this.showDebug = !this.showDebug;
                    }
                },
                
                mounted() {
                    console.log('Bank management app mounted');
                    console.log('Available components:', ComponentLoader.getLoadedFeatures());
                }
            }, '#bankApp');

            if (app) {
                console.log('Bank management app initialized successfully');
                
                // Add debug toggle (for development)
                window.addEventListener('keydown', function(e) {
                    if (e.ctrlKey && e.shiftKey && e.key === 'D') {
                        if (app && app._instance) {
                            app._instance.exposed.toggleDebug();
                        }
                    }
                });
            }
        });
    </script>
}