@page
@{
    ViewData["Title"] = "Fair List";
}

<div id="app" v-cloak>
    <div class="row">
        <div class="col-12">
            <div class="grid-container">
                <div ref="mainGridRef"></div>
            </div>
        </div>
    </div>

    <div class="modal fade" ref="mainModalRef" id="MainModal" tabindex="-1" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ state.mainTitle }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body bg-body-tertiary">
                    <form id="MainForm">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>Fair Info</h5>
                                    </div>
                                    <div class="card-body bg-body-tertiary">
                                        <div class="row mb-3">
                                           <div class="col-md-4">
                                                <label for="Fair Code">Fair Code</label>
                                                <input ref="fairCodeRef" type="text" v-model="state.fairCode">
                                                <label class="text-danger">{{ state.errors.fairCode }}</label>
                                            </div>
                                            <div class="col-md-8">
                                                <label for="Description">Description</label>
                                                <input ref="descriptionRef" type="text" v-model="state.description">
                                                <label class="text-danger">{{ state.errors.description }}</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button"
                            id="MainSaveButton"
                            class="btn"
                            v-bind:class="state.deleteMode ? 'btn-danger' : 'btn-primary'"
                            v-on:click="handler.handleSubmit"
                            v-bind:disabled="state.isSubmitting">
                        <span class="spinner-border spinner-border-sm me-2" v-if="state.isSubmitting" role="status" aria-hidden="true"></span>
                        <span v-if="!state.isSubmitting">{{ state.deleteMode ? 'Delete' : 'Save' }}</span>
                        <span v-else>{{ state.deleteMode ? 'Deleting...' : 'Saving...' }}</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@section scripts {
    <script src="~/FrontEnd/Pages/Fairs/FairList.cshtml.js"></script>
}