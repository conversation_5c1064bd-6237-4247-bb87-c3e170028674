using Application.Features.FairManager.Commands;
using Application.Features.FairManager.Queries;
using ASPNET.BackEnd.Common.Base;
using ASPNET.BackEnd.Common.Models;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace ASPNET.BackEnd.Controllers;

[Route("api/[controller]")]
public class FairController : BaseApiController
{
    public FairController(ISender sender) : base(sender)
    {
    }

    [Authorize]
    [HttpPost("CreateFair")]
    public async Task<ActionResult<ApiSuccessResult<CreateFairResult>>> CreateFairAsync(CreateFairRequest request, CancellationToken cancellationToken)
    {
        var response = await _sender.Send(request, cancellationToken);

        return Ok(new ApiSuccessResult<CreateFairResult>
        {
            Code = StatusCodes.Status200OK,
            Message = $"Success executing {nameof(CreateFairAsync)}",
            Content = response
        });
    }

    [Authorize]
    [HttpPost("UpdateFair")]
    public async Task<ActionResult<ApiSuccessResult<UpdateFairResult>>> UpdateFairAsync(UpdateFairRequest request, CancellationToken cancellationToken)
    {
        var response = await _sender.Send(request, cancellationToken);

        return Ok(new ApiSuccessResult<UpdateFairResult>
        {
            Code = StatusCodes.Status200OK,
            Message = $"Success executing {nameof(UpdateFairAsync)}",
            Content = response
        });
    }

    [Authorize]
    [HttpPost("DeleteFair")]
    public async Task<ActionResult<ApiSuccessResult<DeleteFairResult>>> DeleteFairAsync(DeleteFairRequest request, CancellationToken cancellationToken)
    {
        var response = await _sender.Send(request, cancellationToken);

        return Ok(new ApiSuccessResult<DeleteFairResult>
        {
            Code = StatusCodes.Status200OK,
            Message = $"Success executing {nameof(DeleteFairAsync)}",
            Content = response
        });
    }

    [Authorize]
    [HttpGet("GetFairList")]
    public async Task<ActionResult<ApiSuccessResult<GetFairListResult>>> GetFairListAsync(
        CancellationToken cancellationToken,
        [FromQuery] bool isDeleted = false
        )
    {
        var request = new GetFairListRequest { IsDeleted = isDeleted };
        var response = await _sender.Send(request, cancellationToken);

        return Ok(new ApiSuccessResult<GetFairListResult>
        {
            Code = StatusCodes.Status200OK,
            Message = $"Success executing {nameof(GetFairListAsync)}",
            Content = response
        });
    }
} 