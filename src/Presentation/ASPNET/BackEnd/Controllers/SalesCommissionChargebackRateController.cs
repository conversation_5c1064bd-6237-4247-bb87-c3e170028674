using Application.Features.SalesCommissionChargebackRateManager.Commands;
using Application.Features.SalesCommissionChargebackRateManager.Queries;
using ASPNET.BackEnd.Common.Base;
using ASPNET.BackEnd.Common.Models;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace ASPNET.BackEnd.Controllers;

[Route("api/[controller]")]
public class SalesCommissionChargebackRateController : BaseApiController
{
    public SalesCommissionChargebackRateController(ISender sender) : base(sender)
    {
    }

    [Authorize]
    [HttpPost("CreateSalesCommissionChargebackRate")]
    public async Task<ActionResult<ApiSuccessResult<CreateSalesCommissionChargebackRateResult>>> CreateSalesCommissionChargebackRateAsync(CreateSalesCommissionChargebackRateRequest request, CancellationToken cancellationToken)
    {
        var response = await _sender.Send(request, cancellationToken);

        return Ok(new ApiSuccessResult<CreateSalesCommissionChargebackRateResult>
        {
            Code = StatusCodes.Status200OK,
            Message = $"Success executing {nameof(CreateSalesCommissionChargebackRateAsync)}",
            Content = response
        });
    }

    [Authorize]
    [HttpPost("UpdateSalesCommissionChargebackRate")]
    public async Task<ActionResult<ApiSuccessResult<UpdateSalesCommissionChargebackRateResult>>> UpdateSalesCommissionChargebackRateAsync(UpdateSalesCommissionChargebackRateRequest request, CancellationToken cancellationToken)
    {
        var response = await _sender.Send(request, cancellationToken);

        return Ok(new ApiSuccessResult<UpdateSalesCommissionChargebackRateResult>
        {
            Code = StatusCodes.Status200OK,
            Message = $"Success executing {nameof(UpdateSalesCommissionChargebackRateAsync)}",
            Content = response
        });
    }

    [Authorize]
    [HttpPost("DeleteSalesCommissionChargebackRate")]
    public async Task<ActionResult<ApiSuccessResult<DeleteSalesCommissionChargebackRateResult>>> DeleteSalesCommissionChargebackRateAsync(DeleteSalesCommissionChargebackRateRequest request, CancellationToken cancellationToken)
    {
        var response = await _sender.Send(request, cancellationToken);

        return Ok(new ApiSuccessResult<DeleteSalesCommissionChargebackRateResult>
        {
            Code = StatusCodes.Status200OK,
            Message = $"Success executing {nameof(DeleteSalesCommissionChargebackRateAsync)}",
            Content = response
        });
    }


    [Authorize]
    [HttpGet("GetSalesCommissionChargebackRateList")]
    public async Task<ActionResult<ApiSuccessResult<GetSalesCommissionChargebackRateListResult>>> GetSalesCommissionChargebackRateListAsync(
        CancellationToken cancellationToken,
        [FromQuery] bool isDeleted = false
        )
    {
        var request = new GetSalesCommissionChargebackRateListRequest { IsDeleted = isDeleted };
        var response = await _sender.Send(request, cancellationToken);

        return Ok(new ApiSuccessResult<GetSalesCommissionChargebackRateListResult>
        {
            Code = StatusCodes.Status200OK,
            Message = $"Success executing {nameof(GetSalesCommissionChargebackRateListAsync)}",
            Content = response
        });
    }
}


