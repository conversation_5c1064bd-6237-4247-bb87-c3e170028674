terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "5.70.0"
    }
  }
  required_version = ">= 1.7.5"
}

provider "aws" {
  region = "ap-southeast-5"

  assume_role {
    role_arn = "arn:aws:iam::${var.aws_provision_id}:role/${var.aws_provision_role}"
  }

  default_tags {
    tags = {
      "Terraform"   = "true"
      "Environment" = "dev"
      "Project"     = "DSAS"
    }
  }
}
