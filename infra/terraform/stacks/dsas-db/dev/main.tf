module "rds_sqlserver" {
  source = "../../../modules/rds-sqlserver"
  name   = "dsas-db"
  env    = "dev"

  db_engine           = "sqlserver-ex"
  db_engine_version   = "16.00.4175.1.v1"
  db_instance_class   = "db.t3.micro"
  db_username         = "root"
  multi_az            = false
  publicly_accessible = true
  vpc_id              = "vpc-0ced604f82d48c675" # default VPC
  subnet_ids          = ["subnet-0ec556e58051189e3", "subnet-08e67f442e3b5a5d4"]

  allocated_storage               = 20
  max_allocated_storage           = 40
  deletion_protection             = false
  enabled_cloudwatch_logs_exports = ["agent", "error"] # SQL Server Express doesn't support "trace" and "audit"

  backup_retention_period = 0 # disable automated backups
}

module "rds_scheduler" {
  source = "../../../modules/rds-scheduler"

  name_prefix    = "dsas-db"
  environment    = "dev"
  rds_instance_id = module.rds_sqlserver.db_instance_id

  # Schedule configuration for Singapore timezone (UTC+8)
  start_schedule = "cron(30 0 ? * MON-FRI *)"  # 8:30 AM UTC+8 = 00:30 AM UTC
  stop_schedule  = "cron(59 15 ? * MON-FRI *)" # 11:59 PM UTC+8 = 15:59 PM UTC

  teams_webhook_url = var.teams_webhook_url
}