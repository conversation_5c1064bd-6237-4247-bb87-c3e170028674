output "result" {
  description = "DSAS SQL Server Database information"

  value = {
    db_instance_id       = module.rds_sqlserver.db_instance_id
    db_instance_endpoint = module.rds_sqlserver.db_instance_endpoint

    ssm_db_host     = module.rds_sqlserver.ssm_db_host
    db_port         = module.rds_sqlserver.db_port
    ssm_db_username = module.rds_sqlserver.ssm_db_username
    ssm_db_password = module.rds_sqlserver.ssm_db_password

    ssm_ec2_key      = module.ec2_migration.output.ssm_key_path
    ssm_ec2_password = module.ec2_migration.output.ssm_password_path
    ec2_public_ip    = module.ec2_migration.output.public_ip
    ec2_public_dns   = module.ec2_migration.output.public_dns
  }
}
