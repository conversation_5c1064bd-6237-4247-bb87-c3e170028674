module "ec2_migration" {
  source = "../../../modules/ec2"
  name   = "dsas-migration"
  env    = "uat"
  
  subnet_id                   = local.remote_state_network.public_subnets[0]
  associate_public_ip_address = true

  auto_generate_key = true
  get_password_data = true
  
  type          = "windows"
  volume_size   = 50
  volume_type   = "gp3"
  instance_type = "t3.large"
  
  ingress_rules = [
    { from_port = 3389, to_port = 3389, protocol = "tcp", cidr_blocks = ["***************/32"], description = "Anchor Sprint - Jazz" },
    { from_port = 3389, to_port = 3389, protocol = "tcp", cidr_blocks = ["*************/32"], description = "Anchor Sprint - Bastion" },
  ]

  policy_arns = [
    "arn:aws:iam::aws:policy/AmazonRDSFullAccess",
    "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
  ]
}
  