data "terraform_remote_state" "network" {
  backend = "remote"
  config = {
    organization = "grolier"
    workspaces = {
      name = "network-core-uat"
    }
  }
}

locals {
  remote_state_network = data.terraform_remote_state.network.outputs.result
}

# Security group is already created inside the rds-sqlserver module

module "rds_sqlserver" {
  source = "../../../modules/rds-sqlserver"
  name   = "dsas-db"
  env    = "uat"

  db_engine           = "sqlserver-ex"
  db_engine_version   = "16.00.4175.1.v1"
  db_instance_class   = "db.t3.micro"
  db_username         = "root"
  multi_az            = false
  publicly_accessible = true # Set to false for private access
  vpc_id              = local.remote_state_network.vpc_id
  subnet_ids          = local.remote_state_network.private_subnets
  sg_ingress_cidr_blocks = [
    local.remote_state_network.vpc_cidr_block  # Only allow access from within the VPC
  ]
  allocated_storage               = 20
  max_allocated_storage           = 40
  deletion_protection             = true
  enabled_cloudwatch_logs_exports = ["agent", "error"] # SQL Server Express doesn't support "trace" and "audit"

  backup_retention_period = 0 # Disable automated backups
}

module "rds_scheduler" {
  source = "../../../modules/rds-scheduler"

  name_prefix    = "dsas-db"
  environment    = "uat"
  rds_instance_id = module.rds_sqlserver.db_instance_id

  # Schedule configuration for Singapore timezone (UTC+8)
  start_schedule = "cron(30 0 ? * MON-FRI *)"  # 8:30 AM UTC+8 = 00:30 AM UTC
  stop_schedule  = "cron(59 15 ? * MON-FRI *)" # 11:59 PM UTC+8 = 15:59 PM UTC

  teams_webhook_url = var.teams_webhook_url
}