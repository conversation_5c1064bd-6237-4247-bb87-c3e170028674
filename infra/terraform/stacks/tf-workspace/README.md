# Terraform Workspace Management Module

This module manages Terraform workspaces using Terraform Cloud or Terraform Enterprise. It requires a valid `TFE_TOKEN` to authenticate with Terraform Cloud or Terraform Enterprise and create resources. Additionally, you need to provide the Terraform organization name as a variable.

## Requirements

- Terraform 1.0 or higher
- A valid `TFE_TOKEN` environment variable for authentication
- Terraform Cloud or Terraform Enterprise account