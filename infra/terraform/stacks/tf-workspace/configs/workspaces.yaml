defs:
  oauth_token_id: &oauth_token ot-4H2HBbGgjf1EVThz
  default: &default
    terraform_version: 1.9.7
    global_remote_state: true
  variables: &common_variables
    - &tfc_organization_name
      key: tfc_organization_name
      category: terraform
      value: grolier
    - &TFC_AWS_PROVIDER_AUTH
      key: TFC_AWS_PROVIDER_AUTH
      category: env
      value: true
    - &TFC_AWS_RUN_ROLE_ARN
      key: TFC_AWS_RUN_ROLE_ARN
      category: env
      value: arn:aws:iam::************:role/TFC-Role
    - &DEV_TFC_AWS_RUN_ROLE_ARN
      key: TFC_AWS_RUN_ROLE_ARN
      category: env
      value: arn:aws:iam::************:role/tfc-role
    - &dev_aws_provision_role
      key: aws_provision_role
      category: terraform
      value: OrganizationAccountAccessRole
    - &dev_aws_provision_id
      key: aws_provision_id
      category: terraform
      value: ************

workspace-account-core-dev:
  <<: *default
  name: account-core-dev
  description: Manage all account resources for dev environment
  working_directory: infra/terraform/stacks/account-core/dev
  auto_apply: true
  project_id: project-aws-core
  trigger_prefixes:
    - infra/terraform/stacks/account-core/dev
  vcs_repo:
    branch: main
    identifier: anchorsprint/grolier-dsas
    oauth_token_id: *oauth_token
  variables:
    - *tfc_organization_name
    - *TFC_AWS_PROVIDER_AUTH
    - *DEV_TFC_AWS_RUN_ROLE_ARN
    - *dev_aws_provision_role
    - *dev_aws_provision_id
    - key: tfc_project_name
      category: terraform
      value: Core-AWS-Project

workspace-account-core-uat:
  <<: *default
  name: account-core-uat
  description: Manage all account resources for uat environment
  working_directory: infra/terraform/stacks/account-core/uat
  auto_apply: false
  project_id: project-aws-core
  trigger_prefixes:
    - infra/terraform/stacks/account-core/uat
  vcs_repo:
    branch: main
    identifier: anchorsprint/grolier-dsas
    oauth_token_id: *oauth_token
  variables:
    - *tfc_organization_name
    - *TFC_AWS_PROVIDER_AUTH
    - *TFC_AWS_RUN_ROLE_ARN
    - key: tfc_project_name
      category: terraform
      value: Core-AWS-Project

workspace-account-core-prod:
  <<: *default
  name: account-core-prod
  description: Manage all account resources for prod environment
  working_directory: infra/terraform/stacks/account-core/prod
  auto_apply: false
  project_id: project-aws-core
  trigger_prefixes:
    - infra/terraform/stacks/account-core/prod
  vcs_repo:
    branch: main
    identifier: anchorsprint/grolier-dsas
    oauth_token_id: *oauth_token
  variables:
    - *tfc_organization_name
    - *TFC_AWS_PROVIDER_AUTH
    - *TFC_AWS_RUN_ROLE_ARN
    - key: tfc_project_name
      category: terraform
      value: Core-AWS-Project

workspace-network-core-dev:
  <<: *default
  name: network-core-dev
  description: Manage all network resources for dev environment
  working_directory: infra/terraform/stacks/network-core/dev
  auto_apply: true
  project_id: project-aws-core
  trigger_prefixes:
    - infra/terraform/stacks/network-core/dev
  vcs_repo:
    branch: main
    identifier: anchorsprint/grolier-dsas
    oauth_token_id: *oauth_token
  variables:
    - *tfc_organization_name
    - *TFC_AWS_PROVIDER_AUTH
    - *DEV_TFC_AWS_RUN_ROLE_ARN
    - *dev_aws_provision_role
    - *dev_aws_provision_id
    - key: tfc_project_name
      category: terraform
      value: Core-AWS-Project

workspace-network-core-uat:
  <<: *default
  name: network-core-uat
  description: Manage all network resources for uat environment
  working_directory: infra/terraform/stacks/network-core/uat
  auto_apply: false
  project_id: project-aws-core
  trigger_prefixes:
    - infra/terraform/stacks/network-core/uat
  vcs_repo:
    branch: main
    identifier: anchorsprint/grolier-dsas
    oauth_token_id: *oauth_token
  variables:
    - *tfc_organization_name
    - *TFC_AWS_PROVIDER_AUTH
    - *TFC_AWS_RUN_ROLE_ARN
    - key: tfc_project_name
      category: terraform
      value: Core-AWS-Project

workspace-network-core-prod:
  <<: *default
  name: network-core-prod
  description: Manage all network resources for prod environment
  working_directory: infra/terraform/stacks/network-core/prod
  auto_apply: false
  project_id: project-aws-core
  trigger_prefixes:
    - infra/terraform/stacks/network-core/prod
  vcs_repo:
    branch: main
    identifier: anchorsprint/grolier-dsas
    oauth_token_id: *oauth_token
  variables:
    - *tfc_organization_name
    - *TFC_AWS_PROVIDER_AUTH
    - *TFC_AWS_RUN_ROLE_ARN
    - key: tfc_project_name
      category: terraform
      value: Core-AWS-Project

workspace-dsas-apps-dev:
  <<: *default
  name: dsas-apps-dev
  description: Manage all DSAS resources for dev environment
  working_directory: infra/terraform/stacks/dsas-apps/dev
  auto_apply: true
  project_id: project-dsas-aws
  trigger_prefixes:
    - infra/terraform/stacks/dsas-apps/dev
  vcs_repo:
    branch: main
    identifier: anchorsprint/grolier-dsas
    oauth_token_id: *oauth_token
  variables:
    - *tfc_organization_name
    - *TFC_AWS_PROVIDER_AUTH
    - *DEV_TFC_AWS_RUN_ROLE_ARN
    - *dev_aws_provision_role
    - *dev_aws_provision_id
    - key: tfc_project_name
      category: terraform
      value: DSAS-AWS-Project

workspace-dsas-apps-uat:
  <<: *default
  name: dsas-apps-uat
  description: Manage all DSAS resources for uat environment
  working_directory: infra/terraform/stacks/dsas-apps/uat
  auto_apply: false
  project_id: project-dsas-aws
  trigger_prefixes:
    - infra/terraform/stacks/dsas-apps/uat
  vcs_repo:
    branch: main
    identifier: anchorsprint/grolier-dsas
    oauth_token_id: *oauth_token
  variables:
    - *tfc_organization_name
    - *TFC_AWS_PROVIDER_AUTH
    - *TFC_AWS_RUN_ROLE_ARN
    - key: tfc_project_name
      category: terraform
      value: DSAS-AWS-Project

workspace-dsas-apps-prod:
  <<: *default
  name: dsas-apps-prod
  description: Manage all DSAS resources for prod environment
  working_directory: infra/terraform/stacks/dsas-apps/prod
  auto_apply: false
  project_id: project-dsas-aws
  trigger_prefixes:
    - infra/terraform/stacks/dsas-apps/prod
  vcs_repo:
    branch: main
    identifier: anchorsprint/grolier-dsas
    oauth_token_id: *oauth_token
  variables:
    - *tfc_organization_name
    - *TFC_AWS_PROVIDER_AUTH
    - *TFC_AWS_RUN_ROLE_ARN
    - key: tfc_project_name
      category: terraform
      value: DSAS-AWS-Project

workspace-dsas-db-dev:
  <<: *default
  name: dsas-db-dev
  description: Manage all DSAS Database resources for dev environment
  working_directory: infra/terraform/stacks/dsas-db/dev
  auto_apply: true
  project_id: project-dsas-aws
  trigger_prefixes:
    - infra/terraform/stacks/dsas-db/dev
  vcs_repo:
    branch: main
    identifier: anchorsprint/grolier-dsas
    oauth_token_id: *oauth_token
  variables:
    - *tfc_organization_name
    - *TFC_AWS_PROVIDER_AUTH
    - *DEV_TFC_AWS_RUN_ROLE_ARN
    - *dev_aws_provision_role
    - *dev_aws_provision_id
    - key: tfc_project_name
      category: terraform
      value: DSAS-AWS-Project

workspace-dsas-db-uat:
  <<: *default
  name: dsas-db-uat
  description: Manage all DSAS Database resources for uat environment
  working_directory: infra/terraform/stacks/dsas-db/uat
  auto_apply: false
  project_id: project-dsas-aws
  trigger_prefixes:
    - infra/terraform/stacks/dsas-db/uat
  vcs_repo:
    branch: main
    identifier: anchorsprint/grolier-dsas
    oauth_token_id: *oauth_token
  variables:
    - *tfc_organization_name
    - *TFC_AWS_PROVIDER_AUTH
    - *TFC_AWS_RUN_ROLE_ARN
    - key: tfc_project_name
      category: terraform
      value: DSAS-AWS-Project

workspace-dsas-db-prod:
  <<: *default
  name: dsas-db-prod
  description: Manage all DSAS Database resources for prod environment
  working_directory: infra/terraform/stacks/dsas-db/prod
  auto_apply: false
  project_id: project-dsas-aws
  trigger_prefixes:
    - infra/terraform/stacks/dsas-db/prod
  vcs_repo:
    branch: main
    identifier: anchorsprint/grolier-dsas
    oauth_token_id: *oauth_token
  variables:
    - *tfc_organization_name
    - *TFC_AWS_PROVIDER_AUTH
    - *TFC_AWS_RUN_ROLE_ARN
    - key: tfc_project_name
      category: terraform
      value: DSAS-AWS-Project
