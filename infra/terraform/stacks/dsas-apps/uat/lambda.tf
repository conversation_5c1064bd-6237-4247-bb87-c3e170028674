# Security group for Lambda function in private subnet
resource "aws_security_group" "dsas_lambda_sg" {
  name        = "dsas-lambda-${local.sg_resource_prefix}"
  description = "Security group for DSAS Lambda function"
  vpc_id      = local.remote_state_network.vpc_id

  # Allow outbound traffic to private RDS
  egress {
    from_port   = local.remote_state_database.db_port
    to_port     = local.remote_state_database.db_port
    protocol    = "tcp"
    cidr_blocks = [for subnet in data.aws_subnet.private_subnet_cidrs : subnet.cidr_block]
  }

  # Allow HTTPS outbound to public internet (for CloudWatch, S3, etc via NAT)
  egress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

module "dsas_lambda" {
  source = "../../../modules/lambda-app"

  name = "dsas-lambda"
  env  = local.env

  image_repository_uri = module.ecr-artifact.repository_uri
  image_tag            = "487" # Replace with the build number

  memory_size = 1024
  timeout     = 60
  provisioned_concurrency = 0

  # VPC configuration for private subnet deployment
  vpc_config = {
    subnet_ids         = local.remote_state_network.private_subnets
    security_group_ids = [aws_security_group.dsas_lambda_sg.id]
  }

  enable_apigw          = true
  create_route53_record = true
  domain_name           = "dsas-uat.grolier.com.my"
  route53_zone_id       = data.aws_route53_zone.selected.zone_id

  environment_variables = {
    ASPNETCORE_ENVIRONMENT  = "Production"
    TZ                      = "Asia/Kuala_Lumpur"

    DbSettings__Server   = "SSM:${local.remote_state_database.ssm_db_host}"
    DbSettings__Port     = local.remote_state_database.db_port
    DbSettings__Database = "dsas"
    DbSettings__User     = "SSM:${local.remote_state_database.ssm_db_username}"
    DbSettings__Password = "SSM:${local.remote_state_database.ssm_db_password}"
    DbSettings__TrustServerCertificate = "True"
  }

  policy_attachments = [
    "arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole"
  ]
}
